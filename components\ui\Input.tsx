

import React, { ReactNode, forwardRef } from 'react';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  icon?: ReactNode;
  rightAddon?: ReactNode;
  containerClassName?: string;
}

const Input = forwardRef<HTMLInputElement, InputProps>(({ label, icon, rightAddon, id, containerClassName = '', ...props }, ref) => {
  const inputId = id || `input-${props.name}`;
  return (
    <div className={containerClassName}>
      <label htmlFor={inputId} className="block text-sm font-medium text-gray-700 mb-1">
        {label}
      </label>
      <div className="relative">
        {icon && (
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <span className="text-gray-500">{icon}</span>
          </div>
        )}
        <input
          id={inputId}
          ref={ref}
          className={`w-full bg-gray-50 border border-gray-300 rounded-lg py-2.5 focus:outline-none focus:ring-2 focus:ring-accent/50 focus:border-accent transition-colors
              ${icon ? 'pl-10' : 'pl-4'}
              ${rightAddon ? 'pr-10' : 'pr-4'}
            `}
          {...props}
        />
        {rightAddon && (
              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  {rightAddon}
              </div>
          )}
      </div>
    </div>
  );
});

Input.displayName = 'Input';

export default Input;
