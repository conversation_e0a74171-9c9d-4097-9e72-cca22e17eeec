import React, { useEffect } from 'react';
import { X, Bell, Package, Wrench, Check, Circle } from 'lucide-react';
import { useApp } from '../../context/AppContext';
import type { Notification } from '../../types';

interface NotificationsPanelProps {
    isOpen: boolean;
    onClose: () => void;
}

const timeAgo = (dateString: string): string => {
    if (!dateString) return "never";
    const date = new Date(dateString);
    const now = new Date();
    const seconds = Math.round((now.getTime() - date.getTime()) / 1000);
    const minutes = Math.round(seconds / 60);
    const hours = Math.round(minutes / 60);
    const days = Math.round(hours / 24);

    if (seconds < 5) return "just now";
    if (seconds < 60) return `${seconds}s ago`;
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
};

const NotificationIcon: React.FC<{ type: Notification['type'] }> = ({ type }) => {
    switch (type) {
        case 'order': return <Wrench size={20} />;
        case 'inventory': return <Package size={20} />;
        case 'system': return <Bell size={20} />;
        default: return <Circle size={20} />;
    }
}

const NotificationsPanel: React.FC<NotificationsPanelProps> = ({ isOpen, onClose }) => {
    const { notifications, markNotificationAsRead, markAllNotificationsAsRead } = useApp();

    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'auto';
        }
        return () => {
            document.body.style.overflow = 'auto';
        };
    }, [isOpen]);

    if (!isOpen) return null;

    const unreadCount = notifications.filter(n => !n.read).length;

    return (
        <div role="dialog" aria-modal="true" className="fixed inset-0 z-40">
            {/* Backdrop */}
            <div className="absolute inset-0 bg-black/30" onClick={onClose}></div>

            {/* Panel */}
            <div className="absolute top-0 right-0 h-full w-full max-w-md bg-gray-50 shadow-xl flex flex-col animate-slide-in-right">
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
                    <div className="flex items-center gap-3">
                        <Bell size={24} className="text-accent" />
                        <h2 className="text-lg font-bold text-gray-800">Notifications</h2>
                    </div>
                    <button onClick={onClose} className="p-2 rounded-full text-gray-500 hover:bg-gray-100">
                        <X size={24} />
                    </button>
                </div>
                
                 {/* Sub-Header */}
                {notifications.length > 0 && (
                    <div className="flex justify-between items-center p-4 bg-white border-b border-gray-200 text-sm">
                        <p className="text-gray-600">You have <span className="font-bold text-accent">{unreadCount} unread</span> notifications.</p>
                        <button 
                            onClick={markAllNotificationsAsRead}
                            disabled={unreadCount === 0}
                            className="font-semibold text-accent hover:underline disabled:text-gray-400 disabled:no-underline"
                        >
                            Mark all as read
                        </button>
                    </div>
                )}


                {/* Notifications List */}
                <div className="flex-grow p-2 overflow-y-auto">
                    {notifications.length > 0 ? (
                        <div className="space-y-2">
                            {notifications.map(notif => (
                                <div key={notif.id} className={`p-4 rounded-lg flex items-start gap-4 transition-colors ${notif.read ? 'bg-gray-100' : 'bg-white shadow-sm'}`}>
                                    <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${notif.read ? 'bg-gray-200 text-gray-500' : 'bg-accent-light text-accent'}`}>
                                        <NotificationIcon type={notif.type} />
                                    </div>
                                    <div className="flex-grow">
                                        <p className="text-sm text-gray-700 leading-snug">{notif.message}</p>
                                        <p className={`text-xs mt-1 ${notif.read ? 'text-gray-400' : 'text-accent'}`}>{timeAgo(notif.timestamp)}</p>
                                    </div>
                                    {!notif.read && (
                                        <button onClick={() => markNotificationAsRead(notif.id)} className="p-2 rounded-full text-gray-400 hover:bg-gray-200 hover:text-gray-700 flex-shrink-0" title="Mark as read">
                                            <Check size={18}/>
                                        </button>
                                    )}
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="text-center py-20 px-4">
                            <Bell size={48} className="text-gray-300 mx-auto" />
                            <h3 className="mt-4 text-lg font-semibold text-gray-700">All caught up!</h3>
                            <p className="text-gray-500 mt-1">You have no new notifications.</p>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default NotificationsPanel;
