export enum OrderStatus {
  Received = 'Received',
  Diagnosing = 'Diagnosing',
  RepairInProgress = 'Repair in Progress',
  QualityCheck = 'Quality Check',
  ReadyForDelivery = 'Ready for Delivery',
  Delivered = 'Delivered',
  Cancelled = 'Cancelled'
}

export enum TechnicianRole {
  Owner = 'Owner',
  Technician = 'Technician',
  Cashier = 'Cashier'
}

export enum JobPriority {
  Normal = 'Normal',
  Urgent = 'Urgent',
  VIP = 'VIP',
}

export type DayOfWeek = 'Sunday' | 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday';

export interface BusinessHour {
  day: DayOfWeek;
  isOpen: boolean;
  openTime: string; // e.g., '09:00'
  closeTime: string; // e.g., '20:00'
}

export interface ShopProfile {
  name: string;
  phone: string;
  address: string;
  avatarUrl?: string;
  bannerUrl?: string;
}

export interface Technician {
  id: string;
  name: string;
  role: TechnicianRole;
  avatarUrl?: string;
  phone?: string;
}

export interface Customer {
  id: string;
  name: string;
  phone: string;
  address?: string;
  notes?: string;
  avatarUrl?: string;
}

export interface InventoryItem {
  id: string;
  name: string;
  quantity: number;
  costPrice: number;
  sellingPrice: number;
  lowStockThreshold: number;
}

export interface WholesaleItem {
  id: string;
  name: string;
  category: string;
  supplier: string;
  costPrice: number;
  wholesalePrice: number;
  moq: number; // Minimum Order Quantity
  stock: number;
}

export interface UsedPart {
  inventoryItemId: string;
  quantity: number;
  name: string;
  sellingPrice: number;
}

export interface Order {
  id: string;
  customer: Customer;
  brand?: string;
  deviceModel: string;
  imei?: string;
  serialNo?: string;
  issueDescription: string;
  estimatedCost: number;
  advancePayment: number;
  status: OrderStatus;
  priority: JobPriority;
  createdAt: Date;
  updatedAt: Date;
  photoUrl?: string;
  technicianId?: string;
  usedParts?: UsedPart[];
  laborCost?: number;
}

export type NotificationType = 'order' | 'inventory' | 'system';
export interface Notification {
  id: string;
  message: string;
  type: NotificationType;
  relatedId?: string; // e.g., orderId or inventoryItemId
  timestamp: string; // ISO string
  read: boolean;
}