
import React from 'react';
import { useApp } from '../../context/AppContext';
import Card from '../ui/Card';
import { OrderStatus, Order } from '../../types';
import StatusBadge from '../ui/StatusBadge';
import { 
    <PERSON>ertTriangle, 
    DollarSign, 
    Wrench, 
    Users, 
    PlusCircle, 
    Package, 
    ArrowRight,
    Smartphone,
    ShoppingBag
} from 'lucide-react';

// New component for Quick Actions
const QuickActionButton: React.FC<{ 
    title: string; 
    subtitle: string; 
    icon: React.ReactNode; 
    onClick: () => void;
    className?: string;
}> = ({ title, subtitle, icon, onClick, className = '' }) => (
    <button 
        onClick={onClick}
        className={`flex items-center p-4 rounded-xl shadow-md hover:shadow-lg transition-all transform hover:-translate-y-1 ${className}`}
    >
        <div className="mr-4">{icon}</div>
        <div className="text-left">
            <p className="font-bold text-white">{title}</p>
            <p className="text-sm text-white/80">{subtitle}</p>
        </div>
        <ArrowRight size={20} className="ml-auto text-white/50" />
    </button>
);


// Redesigned StatCard
const StatCard: React.FC<{ 
    title: string; 
    value: string | number; 
    icon: React.ReactElement<{ size?: number }>; 
    color: string;
    onClick?: () => void;
}> = ({ title, value, icon, color, onClick }) => {
    const cardContent = (
        <Card className={`relative overflow-hidden transition-all transform hover:scale-105 ${color}`}>
            <div className="absolute -right-4 -bottom-4 opacity-10">
                {React.cloneElement(icon, { size: 96 })}
            </div>
            <div className="relative text-white">
                <p className="text-sm font-medium opacity-80">{title}</p>
                <p className="text-3xl font-bold mt-1">{value}</p>
            </div>
        </Card>
    );

    if (onClick) {
        return <button onClick={onClick} className="w-full h-full text-left">{cardContent}</button>;
    }
    return cardContent;
};

// New Activity Item for the feed
const ActivityItem: React.FC<{ order: Order }> = ({ order }) => {
    const { viewOrderDetails } = useApp();
    return (
        <button 
            onClick={() => viewOrderDetails(order.id)}
            className="w-full flex items-center gap-4 p-3 hover:bg-gray-100 rounded-lg transition-colors text-left"
        >
            <div className="w-12 h-12 flex-shrink-0 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                {order.photoUrl ? (
                    <img src={order.photoUrl} alt={order.deviceModel} className="w-full h-full object-cover" />
                ) : (
                    <Smartphone size={24} className="text-gray-400" />
                )}
            </div>
            <div className="flex-grow min-w-0">
                <p className="font-semibold text-gray-800 truncate">{order.deviceModel}</p>
                <p className="text-sm text-gray-500 truncate">{order.customer.name}</p>
            </div>
            <div className="flex-shrink-0">
                <StatusBadge status={order.status} />
            </div>
        </button>
    );
}

const DashboardScreen: React.FC = () => {
  const { orders, inventory, customers, wholesaleInventory, setActiveScreen } = useApp();

  const activeRepairs = orders.filter(o => ![
    OrderStatus.Delivered,
    OrderStatus.Cancelled
  ].includes(o.status)).length;
  
  const readyForDelivery = orders.filter(o => o.status === OrderStatus.ReadyForDelivery).length;
  const lowStockAlerts = inventory.filter(i => i.quantity <= i.lowStockThreshold).length;
  const dailyEarnings = orders
    .filter(o => o.status === OrderStatus.Delivered && new Date(o.updatedAt).toDateString() === new Date().toDateString())
    .reduce((sum, order) => sum + order.estimatedCost, 0);
  
  const totalWholesaleItems = wholesaleInventory.length;

  const recentOrders = orders.slice(0, 5);

  return (
    <div className="space-y-6">
      
      {/* Quick Actions */}
      <div className="grid grid-cols-2 gap-3">
          <QuickActionButton 
              title="New Job Card"
              subtitle="Register a new repair"
              icon={<PlusCircle size={28} className="text-white/80" />}
              onClick={() => setActiveScreen('NewOrder')}
              className="bg-gradient-to-r from-accent to-pink-500"
          />
          <QuickActionButton 
              title="Add Customer"
              subtitle="Save new client details"
              icon={<Users size={28} className="text-white/80" />}
              onClick={() => setActiveScreen('Customers')}
              className="bg-gradient-to-r from-blue-500 to-sky-400"
          />
           <QuickActionButton 
              title="Add Stock"
              subtitle="Update retail inventory"
              icon={<Package size={28} className="text-white/80" />}
              onClick={() => setActiveScreen('Inventory')}
              className="bg-gradient-to-r from-green-500 to-emerald-400"
          />
          <QuickActionButton 
              title="Add Wholesale"
              subtitle="Log bulk purchases"
              icon={<ShoppingBag size={28} className="text-white/80" />}
              onClick={() => setActiveScreen('Wholesale')}
              className="bg-gradient-to-r from-orange-500 to-amber-400"
          />
      </div>
      
      {/* Stats Grid */}
      <div className="grid grid-cols-2 gap-4">
        <StatCard 
            title="Active Repairs" 
            value={activeRepairs} 
            icon={<Wrench />} 
            color="bg-blue-500" 
            onClick={() => setActiveScreen('Orders')}
        />
        <StatCard 
            title="Ready for Delivery" 
            value={readyForDelivery} 
            icon={<Package />} 
            color="bg-green-500"
            onClick={() => setActiveScreen('Orders')}
        />
        <StatCard 
            title="Low Stock" 
            value={lowStockAlerts} 
            icon={<AlertTriangle />} 
            color="bg-red-500"
            onClick={() => setActiveScreen('Inventory')}
        />
        <StatCard 
            title="Today's Earnings" 
            value={`₹${dailyEarnings.toLocaleString('en-IN')}`} 
            icon={<DollarSign />} 
            color="bg-purple-500"
            onClick={() => setActiveScreen('Reports')}
        />
        <StatCard 
            title="Wholesale Lines" 
            value={totalWholesaleItems} 
            icon={<ShoppingBag />} 
            color="bg-orange-500"
            onClick={() => setActiveScreen('Wholesale')}
        />
      </div>

      {/* Recent Activity */}
      <Card>
        <h3 className="font-bold text-lg mb-2 text-gray-700">Recent Activity</h3>
        <div className="space-y-2">
          {recentOrders.length > 0 ? (
            recentOrders.map(order => <ActivityItem key={order.id} order={order} />)
          ) : (
            <p className="text-center text-gray-500 py-4">No recent activity.</p>
          )}
        </div>
        {orders.length > 5 && (
            <button 
                onClick={() => setActiveScreen('Orders')} 
                className="w-full mt-4 text-center text-sm font-semibold text-accent hover:underline"
            >
                View All Orders
            </button>
        )}
      </Card>
    </div>
  );
};

export default DashboardScreen;
