import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:camera/camera.dart';

import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
// import 'package:printing/printing.dart';
import 'package:share_plus/share_plus.dart';
import 'add_customer.dart';
import 'order_details.dart';
// import 'customers.dart';

import 'package:path_provider/path_provider.dart';
import '../utils/links.dart';
import 'camera_capture.dart';

import '../app_state.dart';
import '../models.dart';

class NewOrderScreen extends StatefulWidget {
  final Customer? initialCustomer;
  final Order? existingOrder;
  const NewOrderScreen({super.key, this.initialCustomer, this.existingOrder});

  @override
  State<NewOrderScreen> createState() => _NewOrderScreenState();
}

class _NewOrderScreenState extends State<NewOrderScreen> {
  final form = GlobalKey<FormState>();

  final customerName = TextEditingController();
  final customerPhone = TextEditingController();
  final customerAddress = TextEditingController();
  final brand = TextEditingController();
  final deviceModel = TextEditingController();
  final issueDescription = TextEditingController();
  final estimatedCost = TextEditingController();
  final advancePayment = TextEditingController(text: '0');
  JobPriority priority = JobPriority.normal;
  List<File> photos = [];
  String? selectedCustomerId;

  @override
  void dispose() {
    customerName.dispose();
    customerPhone.dispose();
    customerAddress.dispose();
    brand.dispose();
    deviceModel.dispose();
    issueDescription.dispose();
    estimatedCost.dispose();
    advancePayment.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();

    // Initialize for editing existing order
    if (widget.existingOrder != null) {
      final order = widget.existingOrder!;
      customerName.text = order.customer.name;
      customerPhone.text = order.customer.phone;
      customerAddress.text = order.customer.address ?? '';
      selectedCustomerId = order.customer.id;
      brand.text = order.brand ?? '';
      deviceModel.text = order.deviceModel;
      issueDescription.text = order.issueDescription;
      estimatedCost.text = order.estimatedCost.toString();
      advancePayment.text = order.advancePayment.toString();
      priority = order.priority;
      // Note: photos would need to be handled separately if stored as file paths
    }
    // Initialize for new order with pre-selected customer
    else if (widget.initialCustomer != null) {
      final c = widget.initialCustomer!;
      customerName.text = c.name;
      customerPhone.text = c.phone;
      customerAddress.text = c.address ?? '';
      selectedCustomerId = c.id;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        scaffoldBackgroundColor: Colors.black,
        appBarTheme: const AppBarTheme(backgroundColor: Colors.black, foregroundColor: Colors.white),
        colorScheme: Theme.of(context).colorScheme.copyWith(
          primary: const Color(0xFFE50914),
          secondary: const Color(0xFFE50914),
          surface: const Color(0xFF121212),
          background: Colors.black,
          onSurface: Colors.white,
          onBackground: Colors.white,
        ),
        inputDecorationTheme: InputDecorationTheme(
          filled: true,
          fillColor: const Color(0xFF1F1F1F),
          labelStyle: const TextStyle(color: Colors.white70),
          prefixIconColor: Colors.white70,
          suffixIconColor: Colors.white70,
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.white24),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFFE50914)),
          ),
        ),
        textTheme: Theme.of(context).textTheme.apply(bodyColor: Colors.white, displayColor: Colors.white),
        filledButtonTheme: FilledButtonThemeData(
          style: FilledButton.styleFrom(backgroundColor: const Color(0xFFE50914), foregroundColor: Colors.white),
        ),
        outlinedButtonTheme: OutlinedButtonThemeData(
          style: OutlinedButton.styleFrom(foregroundColor: Colors.white, side: const BorderSide(color: Colors.white38)),
        ),
      ),
      child: Scaffold(
        appBar: AppBar(
          title: Text(widget.existingOrder != null ? 'Edit Job Card' : 'New Job Card'),
          actions: widget.existingOrder != null ? [
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () => _showDeleteConfirmation(context),
            ),
          ] : null,
        ),
        body: Form(
          key: form,
        child: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Colors.black, Color(0xFF0A0A0A)],
            ),
          ),
          child: ListView(
            padding: const EdgeInsets.all(20),
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: const Color(0xFF1F1F1F),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.white12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: const Color(0xFFE50914),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(Icons.person, color: Colors.white, size: 20),
                        ),
                        const SizedBox(width: 12),
                        const Text('Customer Details', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.white)),
                      ],
                    ),
                    const SizedBox(height: 20),
            Selector<AppState, List<Customer>>(
              selector: (_, s) => s.customers,
              builder: (context, customers, __) {
                final items = customers
                    .map((c) => DropdownMenuItem<String>(
                          value: c.id,
                          child: Text('${c.name} • ${c.phone}', overflow: TextOverflow.ellipsis, maxLines: 1),
                        ))
                    .toList();
                return Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        isExpanded: true,
                        value: selectedCustomerId,
                        decoration: InputDecoration(
                          labelText: 'Select Customer',
                          filled: true,
                          fillColor: const Color(0xFF2A2A2A),
                          labelStyle: const TextStyle(color: Colors.white70),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: const BorderSide(color: Colors.white24),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: const BorderSide(color: Color(0xFFE50914)),
                          ),
                        ),
                        dropdownColor: const Color(0xFF2A2A2A),
                        items: [
                          ...items,
                          const DropdownMenuItem<String>(
                            value: '__ADD_NEW__',
                            child: Text('+ Add new customer', overflow: TextOverflow.ellipsis, maxLines: 1),
                          ),
                        ],
                        selectedItemBuilder: (context) => [
                          ...customers.map((c) => Text(
                                '${c.name} • ${c.phone}',
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              )),
                          const Text('+ Add new customer', overflow: TextOverflow.ellipsis, maxLines: 1),
                        ],
                        onChanged: (val) async {
                          if (val == '__ADD_NEW__') {
                            final created = await Navigator.of(context).push<Customer>(
                              MaterialPageRoute(builder: (_) => const AddCustomerScreen(silentReturn: true)),
                            );
                            if (created != null) {
                              setState(() {
                                selectedCustomerId = created.id;
                                customerName.text = created.name;
                                customerPhone.text = created.phone;
                                customerAddress.text = created.address ?? '';
                              });
                            }
                          } else {
                            setState(() {
                              selectedCustomerId = val;
                              final c = customers.firstWhere((e) => e.id == val);
                              customerName.text = c.name;
                              customerPhone.text = c.phone;
                              customerAddress.text = c.address ?? '';
                            });
                          }
                        },
                        validator: (v) => (v == null || v.isEmpty) ? 'Please select a customer' : null,
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: () async {
                        final created = await Navigator.of(context).push<Customer>(
                          MaterialPageRoute(builder: (_) => const AddCustomerScreen(silentReturn: true)),
                        );
                        if (created != null) {
                          setState(() {
                            selectedCustomerId = created.id;
                            customerName.text = created.name;
                            customerPhone.text = created.phone;
                            customerAddress.text = created.address ?? '';
                          });
                        }
                      },
                      icon: const Icon(Icons.person_add_alt_1_rounded, color: Color(0xFFE50914)),
                      tooltip: 'Add Customer',
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: customerName,
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(labelText: 'Full Name', prefixIcon: Icon(Icons.person)),
              validator: (v) => (v == null || v.isEmpty) ? 'Required' : null,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: customerPhone,
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(labelText: 'Phone Number', prefixIcon: Icon(Icons.phone)),
              keyboardType: TextInputType.phone,
              validator: (v) => (v == null || v.isEmpty) ? 'Required' : null,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: customerAddress,
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(labelText: 'Address', prefixIcon: Icon(Icons.location_on)),
            ),
                    ],
                  ),
                ),
              const SizedBox(height: 24),
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: const Color(0xFF1F1F1F),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.white12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: const Color(0xFFE50914),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(Icons.photo_camera, color: Colors.white, size: 20),
                        ),
                        const SizedBox(width: 12),
                        const Text('Condition Photo', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.white)),
                      ],
                    ),
                    const SizedBox(height: 20),
                    Wrap(spacing: 12, runSpacing: 12, children: [
                      if (!kIsWeb && !(Platform.isWindows || Platform.isLinux || Platform.isMacOS))
                        Container(
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(colors: [Color(0xFFE50914), Color(0xFFB20710)]),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: ElevatedButton.icon(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.transparent,
                              shadowColor: Colors.transparent,
                              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                            ),
                            onPressed: () async {
                              final path = await Navigator.of(context).push<String>(
                                MaterialPageRoute(builder: (_) => const CameraCaptureScreen(initialLens: CameraLensDirection.back)),
                              );
                              if (path != null) setState(() => photos = [...photos, File(path)]);
                            },
                            icon: const Icon(Icons.photo_camera, color: Colors.white),
                            label: const Text('Live Camera', style: TextStyle(color: Colors.white)),
                          ),
                        ),
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.white38),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: ElevatedButton.icon(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            shadowColor: Colors.transparent,
                            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                          ),
                          onPressed: () async {
                            final picker = ImagePicker();
                            final x = await picker.pickMultiImage(imageQuality: 85);
                            if (x != null && x.isNotEmpty) setState(() => photos = [...photos, ...x.map((e) => File(e.path))]);
                          },
                          icon: const Icon(Icons.photo_library, color: Colors.white),
                          label: const Text('Gallery', style: TextStyle(color: Colors.white)),
                        ),
                      ),
                    ]),
                    const SizedBox(height: 16),
                    if (photos.isNotEmpty)
                      SizedBox(
                        height: 120,
                        child: ListView.separated(
                          scrollDirection: Axis.horizontal,
                          itemBuilder: (_, i) => ClipRRect(
                            borderRadius: BorderRadius.circular(16),
                            child: Stack(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(color: const Color(0xFFE50914), width: 2),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(14),
                                    child: Image.file(photos[i], width: 160, height: 120, fit: BoxFit.cover),
                                  ),
                                ),
                                Positioned(
                                  right: 6,
                                  top: 6,
                                  child: InkWell(
                                    onTap: () => setState(() => photos = [...photos]..removeAt(i)),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: const Color(0xFFE50914),
                                        borderRadius: BorderRadius.circular(20),
                                        boxShadow: [BoxShadow(color: Colors.black26, blurRadius: 4)],
                                      ),
                                      padding: const EdgeInsets.all(6),
                                      child: const Icon(Icons.close, size: 16, color: Colors.white),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          separatorBuilder: (_, __) => const SizedBox(width: 12),
                          itemCount: photos.length,
                        ),
                      ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: const Color(0xFF1F1F1F),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.white12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: const Color(0xFFE50914),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(Icons.smartphone, color: Colors.white, size: 20),
                        ),
                        const SizedBox(width: 12),
                        const Text('Device Details', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.white)),
                      ],
                    ),
                    const SizedBox(height: 20),
                    TextFormField(
                      controller: brand,
                      style: const TextStyle(color: Colors.white),
                      decoration: const InputDecoration(labelText: 'Brand (e.g., Apple, Samsung)', prefixIcon: Icon(Icons.tag)),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: deviceModel,
                      style: const TextStyle(color: Colors.white),
                      decoration: const InputDecoration(labelText: 'Model (e.g., iPhone 13 Pro)', prefixIcon: Icon(Icons.smartphone)),
                      validator: (v) => (v == null || v.isEmpty) ? 'Required' : null,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: const Color(0xFF1F1F1F),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.white12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: const Color(0xFFE50914),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(Icons.build, color: Colors.white, size: 20),
                        ),
                        const SizedBox(width: 12),
                        const Text('Job Details', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.white)),
                      ],
                    ),
                    const SizedBox(height: 20),
                    TextFormField(
                      controller: issueDescription,
                      style: const TextStyle(color: Colors.white),
                      decoration: const InputDecoration(labelText: 'Problem Description'),
                      maxLines: 3,
                      validator: (v) => (v == null || v.isEmpty) ? 'Required' : null,
                    ),
                    const SizedBox(height: 20),
                    Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFF2A2A2A),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.white24),
                      ),
                      child: SegmentedButton<JobPriority>(
                        style: SegmentedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          foregroundColor: Colors.white70,
                          selectedForegroundColor: Colors.white,
                          selectedBackgroundColor: const Color(0xFFE50914),
                        ),
                        segments: const [
                          ButtonSegment(value: JobPriority.normal, label: Text('Normal')),
                          ButtonSegment(value: JobPriority.urgent, label: Text('Urgent')),
                          ButtonSegment(value: JobPriority.vip, label: Text('VIP')),
                        ],
                        selected: {priority},
                        onSelectionChanged: (s) => setState(() => priority = s.first),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: const Color(0xFF1F1F1F),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.white12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: const Color(0xFFE50914),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(Icons.currency_rupee, color: Colors.white, size: 20),
                        ),
                        const SizedBox(width: 12),
                        const Text('Financials', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.white)),
                      ],
                    ),
                    const SizedBox(height: 20),
                    TextFormField(
                      controller: estimatedCost,
                      style: const TextStyle(color: Colors.white),
                      decoration: const InputDecoration(labelText: 'Final Estimated Cost (₹)', prefixIcon: Icon(Icons.currency_rupee)),
                      keyboardType: TextInputType.number,
                      validator: (v) => (v == null || v.isEmpty) ? 'Required' : null,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: advancePayment,
                      style: const TextStyle(color: Colors.white),
                      decoration: const InputDecoration(labelText: 'Advance Payment (₹)', prefixIcon: Icon(Icons.currency_rupee)),
                      keyboardType: TextInputType.number,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),
              Container(
                width: double.infinity,
                height: 52,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFFE50914), Color(0xFFB20710)],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFFE50914).withOpacity(0.3),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: FilledButton(
                  style: FilledButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    shadowColor: Colors.transparent,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  ),
                  onPressed: () async {
                if (!form.currentState!.validate()) return;
                final state = context.read<AppState>();

                Order order;
                if (widget.existingOrder != null) {
                  // Update existing order
                  final existingOrder = widget.existingOrder!;
                  order = Order(
                    id: existingOrder.id,
                    customer: Customer(
                      id: selectedCustomerId ?? existingOrder.customer.id,
                      name: customerName.text.trim(),
                      phone: customerPhone.text.trim(),
                      address: customerAddress.text.trim().isEmpty ? null : customerAddress.text.trim(),
                    ),
                    brand: brand.text.trim().isEmpty ? null : brand.text.trim(),
                    deviceModel: deviceModel.text.trim(),
                    imei: existingOrder.imei,
                    serialNo: existingOrder.serialNo,
                    issueDescription: issueDescription.text.trim(),
                    estimatedCost: double.tryParse(estimatedCost.text) ?? 0,
                    advancePayment: double.tryParse(advancePayment.text) ?? 0,
                    status: existingOrder.status,
                    priority: priority,
                    createdAt: existingOrder.createdAt,
                    updatedAt: DateTime.now(),
                    photoUrl: photos.isNotEmpty ? photos.first.path : existingOrder.photoUrl,
                    photoUrls: photos.map((f) => f.path).toList(),
                    technicianId: existingOrder.technicianId,
                    usedParts: existingOrder.usedParts,
                    laborCost: existingOrder.laborCost,
                  );
                  await state.updateOrder(order);
                } else {
                  // Create new order
                  final id = 'ORD-${DateTime.now().millisecondsSinceEpoch}';
                  order = await state.createJobCard(
                    id: id,
                    customerName: customerName.text.trim(),
                    customerPhone: customerPhone.text.trim(),
                    customerId: selectedCustomerId,
                    customerAddress: customerAddress.text.trim().isEmpty ? null : customerAddress.text.trim(),
                    brand: brand.text.trim().isEmpty ? null : brand.text.trim(),
                    deviceModel: deviceModel.text.trim(),
                    issueDescription: issueDescription.text.trim(),
                    estimatedCost: double.tryParse(estimatedCost.text) ?? 0,
                    advancePayment: double.tryParse(advancePayment.text) ?? 0,
                    priority: priority,
                    photoUrl: photos.isNotEmpty ? photos.first.path : null,
                    photoUrls: photos.map((f) => f.path).toList(),
                  );
                }
                if (!mounted) return;

                // Build a professional PDF with photo
                final doc = pw.Document();
                final rupee = String.fromCharCode(8377);

                // Add photo to PDF if available
                List<pw.Widget> pdfWidgets = [
                  pw.Text('Job Card', style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold)),
                  pw.SizedBox(height: 16),
                  pw.Row(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Expanded(
                        flex: 2,
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Order ID: ${order.id}', style: pw.TextStyle(fontSize: 12)),
                            pw.SizedBox(height: 4),
                            pw.Text('Customer: ${order.customer.name}', style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold)),
                            pw.Text('Phone: ${order.customer.phone}', style: pw.TextStyle(fontSize: 12)),
                            if (order.customer.address != null) pw.Text('Address: ${order.customer.address}', style: pw.TextStyle(fontSize: 12)),
                            pw.SizedBox(height: 8),
                            pw.Text('Device: ${(order.brand ?? '')} ${order.deviceModel}', style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold)),
                            pw.Text('Issue: ${order.issueDescription}', style: pw.TextStyle(fontSize: 12)),
                            pw.SizedBox(height: 8),
                            pw.Container(
                              padding: const pw.EdgeInsets.all(8),
                              decoration: pw.BoxDecoration(
                                border: pw.Border.all(color: PdfColors.grey),
                                borderRadius: pw.BorderRadius.circular(4),
                              ),
                              child: pw.Column(
                                crossAxisAlignment: pw.CrossAxisAlignment.start,
                                children: [
                                  pw.Text('Financial Summary', style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold)),
                                  pw.SizedBox(height: 4),
                                  pw.Text('Estimated Cost: $rupee${order.estimatedCost.toStringAsFixed(0)}', style: pw.TextStyle(fontSize: 12)),
                                  pw.Text('Advance Payment: $rupee${order.advancePayment.toStringAsFixed(0)}', style: pw.TextStyle(fontSize: 12)),
                                  pw.Text('Amount Due: $rupee${(order.estimatedCost - order.advancePayment).toStringAsFixed(0)}', style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold)),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (photos.isNotEmpty) ...[
                        pw.SizedBox(width: 16),
                        pw.Expanded(
                          flex: 1,
                          child: pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              pw.Text('Device Photos', style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold)),
                              pw.SizedBox(height: 4),
                              ...[
                                for (final f in photos.take(3))
                                  pw.Padding(
                                    padding: const pw.EdgeInsets.only(bottom: 6),
                                    child: pw.Image(
                                      pw.MemoryImage(await f.readAsBytes()),
                                      width: 120,
                                      height: 90,
                                      fit: pw.BoxFit.cover,
                                    ),
                                  )
                              ],
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                  pw.SizedBox(height: 16),
                  pw.Container(
                    padding: const pw.EdgeInsets.all(8),
                    decoration: pw.BoxDecoration(
                      color: PdfColors.grey100,
                      borderRadius: pw.BorderRadius.circular(4),
                    ),
                    child: pw.Text(
                      'Generated on: ${DateTime.now().toString().split('.')[0]}',
                      style: pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
                    ),
                  ),
                ];

                doc.addPage(
                  pw.Page(
                    build: (pw.Context ctx) => pw.Padding(
                      padding: const pw.EdgeInsets.all(20),
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: pdfWidgets,
                      ),
                    ),
                  ),
                );

                final bytes = await doc.save();
                final dir = await getTemporaryDirectory();
                final filePath = '${dir.path}/JobCard-${order.id}.pdf';
                final out = File(filePath);
                await out.writeAsBytes(bytes, flush: true);

                // Share and WhatsApp
                await Share.shareXFiles([XFile(out.path)], text: 'Job Card ${order.id}');

                final amountDue = order.estimatedCost - order.advancePayment;
                final message = 'Hello ${order.customer.name},\n\n'
                    'Thank you for choosing our service!\n\n'
                    'Order ID: ${order.id}\n'
                    'Device: ${(order.brand ?? '')} ${order.deviceModel}\n'
                    'Problem: ${order.issueDescription}\n\n'
                    'Estimated: $rupee${order.estimatedCost.toStringAsFixed(0)}\n'
                    'Advance: $rupee${order.advancePayment.toStringAsFixed(0)}\n'
                    'Amount Due: $rupee${amountDue.toStringAsFixed(0)}';
                await launchWhatsAppText(order.customer.phone, message);

                if (!mounted) return;
                // Take user straight to Repair Order screen
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(builder: (_) => OrderDetailsScreen(orderId: order.id)),
                );
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(widget.existingOrder != null ? 'Order updated and shared' : 'Order created and shared'),
                        backgroundColor: const Color(0xFFE50914),
                      ),
                    );
                  },
                  child: Text(
                    widget.existingOrder != null ? 'Update Job Card' : 'Create Job Card',
                    style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                height: 44,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.white38),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: OutlinedButton.icon(
                  style: OutlinedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    side: BorderSide.none,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  ),
                  onPressed: () => launchTel(customerPhone.text.trim()),
                  icon: const Icon(Icons.phone, color: Colors.white, size: 18),
                  label: const Text(
                    'Call Customer',
                    style: TextStyle(color: Colors.white, fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
  }

  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1F1F1F),
          title: const Text('Delete Job Card', style: TextStyle(color: Colors.white)),
          content: const Text(
            'Are you sure you want to delete this job card? This action cannot be undone.',
            style: TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel', style: TextStyle(color: Colors.white70)),
            ),
            Container(
              decoration: BoxDecoration(
                gradient: const LinearGradient(colors: [Colors.red, Color(0xFFB71C1C)]),
                borderRadius: BorderRadius.circular(8),
              ),
              child: TextButton(
                onPressed: () {
                  Navigator.of(context).pop(); // Close dialog
                  _deleteOrder(context);
                },
                style: TextButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Delete'),
              ),
            ),
          ],
        );
      },
    );
  }

  void _deleteOrder(BuildContext context) {
    if (widget.existingOrder != null) {
      final appState = Provider.of<AppState>(context, listen: false);
      appState.deleteOrder(widget.existingOrder!.id);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Job card deleted successfully'),
          backgroundColor: Colors.red,
        ),
      );

      Navigator.of(context).pop(); // Go back to previous screen
    }
  }
}


