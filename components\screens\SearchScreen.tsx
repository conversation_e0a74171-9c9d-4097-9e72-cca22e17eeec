import React, { useState, useEffect } from 'react';
import { GoogleGenAI, GenerateContentResponse } from '@google/genai';
import { useApp } from '../../context/AppContext';
import Card from '../ui/Card';
import MarkdownRenderer from '../ui/MarkdownRenderer';
import { ExternalLink, Frown, Link, BrainCircuit, Loader2 } from 'lucide-react';

const SkeletonLoader: React.FC = () => (
    <div className="space-y-6 animate-pulse">
        <Card className="!p-6 space-y-4">
            <div className="h-5 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-full"></div>
            <div className="h-4 bg-gray-200 rounded w-full"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
        </Card>
        <Card className="!p-6 space-y-4">
            <div className="h-5 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-full"></div>
            <div className="h-4 bg-gray-200 rounded w-full"></div>
        </Card>
    </div>
);


const SearchScreen: React.FC = () => {
    const { searchQuery } = useApp();
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [result, setResult] = useState<{ text: string; sources: any[] } | null>(null);

    useEffect(() => {
        if (!searchQuery) {
            setLoading(false);
            setError("No search query provided.");
            return;
        }

        const fetchResults = async () => {
            setLoading(true);
            setError(null);
            setResult(null);

            try {
                // IMPORTANT: Replace with your actual API key, ideally from a secure environment variable.
                // This is a placeholder and should not be used in production.
                const apiKey = process.env.API_KEY;
                if (!apiKey) {
                    throw new Error("API_KEY environment variable not set.");
                }
                const ai = new GoogleGenAI({ apiKey });

                const response: GenerateContentResponse = await ai.models.generateContent({
                    model: 'gemini-2.5-flash',
                    contents: searchQuery,
                    config: {
                        tools: [{ googleSearch: {} }],
                    },
                });
                
                const text = response.text;
                const sources = response.candidates?.[0]?.groundingMetadata?.groundingChunks ?? [];
                
                setResult({ text, sources });

            } catch (err) {
                console.error("Gemini API Error:", err);
                setError("Sorry, something went wrong while fetching results. Please try again.");
            } finally {
                setLoading(false);
            }
        };

        fetchResults();
    }, [searchQuery]);
    
    if (loading) {
        return <SkeletonLoader />;
    }

    if (error) {
        return (
            <Card className="text-center p-8">
                <Frown className="mx-auto h-12 w-12 text-red-400" />
                <h3 className="mt-4 text-lg font-medium text-gray-900">Search Failed</h3>
                <p className="mt-1 text-sm text-gray-500">{error}</p>
            </Card>
        );
    }
    
    if (!result || !result.text) {
         return (
            <Card className="text-center p-8">
                <BrainCircuit className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-4 text-lg font-medium text-gray-900">No results found</h3>
                <p className="mt-1 text-sm text-gray-500">Could not find an answer for your query. Try rephrasing your search.</p>
            </Card>
        );
    }

    return (
        <div className="space-y-6">
            <div>
                <p className="text-sm text-gray-500">Showing results for:</p>
                <h2 className="text-xl font-bold text-gray-800">{searchQuery}</h2>
            </div>

            <Card className="!p-6">
                <MarkdownRenderer content={result.text} />
            </Card>

            {result.sources && result.sources.length > 0 && (
                 <Card className="!p-6">
                    <h3 className="font-bold text-lg text-gray-700 mb-3">Sources</h3>
                    <ul className="space-y-3">
                        {result.sources.map((source, index) => (
                             <li key={index} className="flex items-start gap-3">
                                <div className="bg-accent-light text-accent p-2 rounded-full mt-1">
                                    <Link size={16} />
                                </div>
                                <div>
                                    <a 
                                        href={source.web.uri} 
                                        target="_blank" 
                                        rel="noopener noreferrer"
                                        className="text-accent font-semibold hover:underline"
                                    >
                                        {source.web.title || 'Untitled Source'}
                                    </a>
                                    <p className="text-xs text-gray-500 break-all">{source.web.uri}</p>
                                </div>
                             </li>
                        ))}
                    </ul>
                </Card>
            )}
        </div>
    );
};

export default SearchScreen;