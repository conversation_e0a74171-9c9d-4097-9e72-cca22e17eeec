<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Repair Shop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              'meesho-pink': '#f43397',
              'meesho-purple': '#6a1b9a',
              'accent': 'var(--color-accent, #f43397)',
              'accent-light': 'var(--color-accent-light, #fef2f8)',
            },
            keyframes: {
              'fade-in-down': {
                '0%': {
                  opacity: '0',
                  transform: 'translateY(-10px)'
                },
                '100%': {
                  opacity: '1',
                  transform: 'translateY(0)'
                },
              },
              'slide-in-right': {
                '0%': {
                  transform: 'translateX(100%)',
                  opacity: '0'
                },
                '100%': {
                  transform: 'translateX(0)',
                  opacity: '1'
                },
              },
              'badge-pulse': {
                '0%, 100%': { transform: 'scale(1)' },
                '50%': { transform: 'scale(1.15)' },
              }
            },
            animation: {
              'fade-in-down': 'fade-in-down 0.5s ease-out forwards',
              'slide-in-right': 'slide-in-right 0.3s ease-out forwards',
              'badge-pulse': 'badge-pulse 1.5s infinite',
            },
          },
        },
      }
    </script>
    <style>
      :root {
        --color-accent: #f43397; /* Default meesho-pink */
        --color-accent-light: #fef2f8;
      }
      .theme-pink {
        --color-accent: #f43397;
        --color-accent-light: #fef2f8;
      }
      .theme-blue {
        --color-accent: #3b82f6;
        --color-accent-light: #eff6ff;
      }
      .theme-green {
        --color-accent: #16a34a;
        --color-accent-light: #f0fdf4;
      }
    </style>
  <script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.1/",
    "react": "https://esm.sh/react@^19.1.1",
    "lucide-react": "https://esm.sh/lucide-react@^0.537.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.1/",
    "recharts": "https://esm.sh/recharts@^3.1.2",
    "@google/genai": "https://esm.sh/@google/genai@^0.15.0",
    "jspdf": "https://esm.sh/jspdf@2.5.1",
    "html2canvas": "https://esm.sh/html2canvas@1.4.1"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body class="bg-gray-50">
    <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>