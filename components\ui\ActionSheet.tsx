
import React, { ReactNode, useEffect } from 'react';
import Button from './Button';

interface ActionSheetProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: ReactNode;
}

const ActionSheet: React.FC<ActionSheetProps> = ({ isOpen, onClose, title, children }) => {
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };
    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }
    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div role="dialog" aria-modal="true" aria-labelledby="action-sheet-title" className="fixed inset-0 z-[60]">
      <div
        className="absolute inset-0 bg-black/50 transition-opacity"
        onClick={onClose}
        aria-hidden="true"
      />
      <div className={`fixed bottom-0 left-0 right-0 max-w-lg mx-auto bg-white rounded-t-2xl shadow-xl transition-transform transform ${isOpen ? 'translate-y-0' : 'translate-y-full'}`}>
        <div className="p-4">
            <h2 id="action-sheet-title" className="text-lg font-bold text-center text-gray-800 mb-2">{title}</h2>
            <div className="flex flex-col gap-2">
                {children}
            </div>
             <Button variant="secondary" onClick={onClose} className="w-full mt-4 !bg-gray-200 !text-gray-800">
                Cancel
            </Button>
        </div>
      </div>
    </div>
  );
};

export default ActionSheet;
