import React from 'react';
import { useApp } from '../../context/AppContext';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';
import Card from '../ui/Card';
import { Order, OrderStatus } from '../../types';

const ReportsScreen: React.FC = () => {
  const { orders, technicians } = useApp();

  // Revenue data for the last 7 days
  const last7DaysRevenue = Array(7).fill(0).map((_, i) => {
    const d = new Date();
    d.setDate(d.getDate() - i);
    const day = d.toLocaleDateString('en-US', { weekday: 'short' });
    const revenue = orders
      .filter(o => o.status === OrderStatus.Delivered && new Date(o.updatedAt).toDateString() === d.toDateString())
      .reduce((sum, order) => sum + order.estimatedCost, 0);
    return { name: day, revenue };
  }).reverse();

  // Most repaired models data
  const modelCounts = orders.reduce((acc, order) => {
    acc[order.deviceModel] = (acc[order.deviceModel] || 0) + 1;
    return acc;
  }, {} as { [key: string]: number });
  
  const mostRepairedModels = Object.entries(modelCounts)
    .map(([name, value]) => ({ name, value }))
    .sort((a, b) => b.value - a.value)
    .slice(0, 5);
    
  const PIE_COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088fe'];

  // Technician performance data
  const technicianPerformance = technicians.map(tech => {
    const completedCount = orders.filter(
      o => o.technicianId === tech.id && (o.status === OrderStatus.ReadyForDelivery || o.status === OrderStatus.Delivered)
    ).length;
    return { name: tech.name.split(' ')[0], completed: completedCount };
  }).sort((a,b) => b.completed - a.completed);


  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-gray-800">Reports & Analytics</h2>

      <Card>
        <h3 className="font-bold text-lg mb-4 text-gray-700">Weekly Revenue</h3>
        <div style={{ width: '100%', height: 300 }}>
          <ResponsiveContainer>
            <BarChart data={last7DaysRevenue} margin={{ top: 5, right: 20, left: -10, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip formatter={(value: number) => `₹${value.toLocaleString()}`} />
              <Legend />
              <Bar dataKey="revenue" fill="var(--color-accent)" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </Card>

      <Card>
        <h3 className="font-bold text-lg mb-4 text-gray-700">Technician Performance</h3>
        <p className="text-sm text-gray-500 -mt-3 mb-4">Completed & Delivered Jobs</p>
        <div style={{ width: '100%', height: 300 }}>
          <ResponsiveContainer>
            <BarChart data={technicianPerformance} margin={{ top: 5, right: 20, left: -10, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis allowDecimals={false} />
              <Tooltip formatter={(value: number) => [`${value} jobs`]} />
              <Legend />
              <Bar dataKey="completed" name="Completed Jobs" fill="#82ca9d" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </Card>
      
      <Card>
        <h3 className="font-bold text-lg mb-4 text-gray-700">Most Repaired Models</h3>
        <div style={{ width: '100%', height: 300 }}>
          <ResponsiveContainer>
            <PieChart>
              <Pie
                data={mostRepairedModels}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} (${(percent ? percent * 100 : 0).toFixed(0)}%)`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {mostRepairedModels.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={PIE_COLORS[index % PIE_COLORS.length]} />
                ))}
              </Pie>
              <Tooltip formatter={(value: number, name) => [`${value} repairs`, name]} />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </Card>
    </div>
  );
};

export default ReportsScreen;