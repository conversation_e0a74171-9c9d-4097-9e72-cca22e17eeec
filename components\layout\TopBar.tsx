import React, { useRef, useState, useEffect } from 'react';
import { Bell, MessageCircle, Settings, ArrowLeft, User, Search, Mic, QrCode } from 'lucide-react';
import { useApp } from '../../context/AppContext';
import useSpeechRecognition from '../../hooks/useSpeechRecognition';
import ScannerModal from '../ui/ScannerModal';
import ChatPanel from '../ui/ChatPanel';
import NotificationsPanel from '../ui/NotificationsPanel';

interface TopBarProps {
  screen: string;
}

const mainScreens = ['Home', 'Orders', 'Inventory', 'Technicians', 'Customers', 'Reports', 'Wholesale'];

const screenTitles: { [key: string]: string } = {
    Settings: "Settings",
    ShopProfile: "Shop Profile",
    ManageTechnicians: "Manage Technicians",
    BusinessHours: "Business Hours",
    DataSync: "Data & Sync",
    About: "About",
    Search: "Search Results",
    NewOrder: "New Job Card",
    OrderDetails: "Order Details",
    EditOrder: "Edit Order",
    CustomerProfile: "Customer Profile",
};

const GoogleIcon: React.FC = () => (
    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M22.56 12.25C22.56 11.45 22.49 10.68 22.36 9.92H12V14.45H18.02C17.75 15.95 17.02 17.24 15.89 18.06V20.72H19.67C21.58 18.91 22.56 15.89 22.56 12.25Z" fill="#4285F4"/>
        <path d="M12 23C15.24 23 17.95 21.92 19.67 20.72L15.89 18.06C14.78 18.82 13.49 19.25 12 19.25C9.27 19.25 6.94 17.45 6.13 15.05H2.25V17.81C4.01 21.09 7.72 23 12 23Z" fill="#34A853"/>
        <path d="M6.13 15.05C5.88 14.35 5.75 13.58 5.75 12.75C5.75 11.92 5.88 11.15 6.13 10.45V7.69H2.25C1.46 9.25 1 10.92 1 12.75C1 14.58 1.46 16.25 2.25 17.81L6.13 15.05Z" fill="#FBBC05"/>
        <path d="M12 5.75C13.64 5.75 15.05 6.33 16.03 7.27L19.74 3.56C17.95 1.86 15.24 1 12 1C7.72 1 4.01 3.91 2.25 7.69L6.13 10.45C6.94 8.05 9.27 5.75 12 5.75Z" fill="#EA4335"/>
    </svg>
);


const TopBar: React.FC<TopBarProps> = ({ screen }) => {
  const { setActiveScreen, shopProfile, setSearchQuery, setFilterQuery, setToast, unreadNotificationsCount } = useApp();
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [isScannerOpen, setIsScannerOpen] = useState(false);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  
  const {
      isListening,
      transcript,
      startListening,
      error: speechError,
      hasRecognitionSupport,
  } = useSpeechRecognition();

  useEffect(() => {
    if (transcript && !isListening) {
      if (searchInputRef.current) {
        searchInputRef.current.value = transcript;
        executeSearch();
      }
    }
  }, [transcript, isListening]);
  
  useEffect(() => {
    if (speechError) {
      setToast(speechError);
    }
  }, [speechError]);
  
  const isMainScreen = mainScreens.includes(screen);
  const showSettingsAndNotifications = mainScreens.includes(screen) || screen === 'Settings';
  const title = screenTitles[screen] || 'RepairShop';

  const goBack = () => {
      if (screen === 'Search') {
          setActiveScreen('Home');
      } else if (screen === 'NewOrder' || screen === 'OrderDetails' || screen === 'CustomerProfile') {
          setActiveScreen(screen === 'CustomerProfile' ? 'Customers' : 'Orders');
      } else if (screen === 'EditOrder') {
          setActiveScreen('OrderDetails');
      }
       else {
          setActiveScreen('Settings');
      }
  }

  const executeSearch = () => {
    const query = searchInputRef.current?.value.trim() ?? '';
    if (!query) return;

    if (screen === 'Home') {
      setSearchQuery(query);
      setActiveScreen('Search');
      if (searchInputRef.current) {
        searchInputRef.current.value = "";
      }
    } else {
      setFilterQuery(query);
    }
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    executeSearch();
  };
  
  const handleScan = (data: string) => {
    setIsScannerOpen(false);
    if (searchInputRef.current) {
        searchInputRef.current.value = data;
        executeSearch();
    }
  };

  const hasBanner = isMainScreen && shopProfile.bannerUrl;
  const showSearchBar = mainScreens.includes(screen) && screen !== 'Reports';
  
  const searchPlaceholders: { [key: string]: string } = {
    Home: 'Search with Google...',
    Orders: 'Search by ID, model, customer...',
    Inventory: 'Search parts by name...',
    Wholesale: 'Search by item, category, supplier...',
    Technicians: 'Search technicians by name...',
    Customers: 'Search customers by name or phone...',
  };
  const placeholder = searchPlaceholders[screen] || 'Search...';


  const Icons = () => (
    <div className={`flex items-center gap-4 ${hasBanner ? 'text-white' : 'text-gray-500'}`}>
        <button className="hover:opacity-80 transition-opacity" onClick={() => setIsChatOpen(true)}>
            <MessageCircle size={24} />
        </button>
        <button className="relative hover:opacity-80 transition-opacity" onClick={() => setIsNotificationsOpen(true)}>
            <Bell size={24} />
            {unreadNotificationsCount > 0 && (
                <span className="absolute -top-1 -right-1.5 flex h-4 w-4">
                  <span className="animate-badge-pulse absolute inline-flex h-full w-full rounded-full bg-accent opacity-75"></span>
                  <span className="relative inline-flex rounded-full h-4 w-4 bg-accent text-white text-[10px] items-center justify-center">
                    {unreadNotificationsCount}
                  </span>
                </span>
            )}
        </button>
        <button className="hover:opacity-80 transition-opacity" onClick={() => setActiveScreen('Settings')}>
            <Settings size={24} />
        </button>
    </div>
  );

  return (
    <>
      <ScannerModal isOpen={isScannerOpen} onClose={() => setIsScannerOpen(false)} onScan={handleScan} />
      <ChatPanel isOpen={isChatOpen} onClose={() => setIsChatOpen(false)} />
      <NotificationsPanel isOpen={isNotificationsOpen} onClose={() => setIsNotificationsOpen(false)} />

      <header className={`sticky top-0 z-10 shadow-sm ${hasBanner ? 'text-white' : 'bg-white'}`}>
        <div 
          className="relative"
          style={hasBanner ? { 
              backgroundImage: `url(${shopProfile.bannerUrl})`, 
              backgroundSize: 'cover', 
              backgroundPosition: 'center' 
          } : {}}
        >
          {hasBanner && <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-black/20 z-0"></div>}
          
          <div className={`relative z-10 flex flex-col justify-between ${hasBanner ? 'h-40 p-4' : ''}`}>
              <div className={`flex justify-between items-start ${hasBanner ? '' : 'p-4'}`}>
                  {isMainScreen ? (
                  <div className="flex items-center gap-3">
                      {shopProfile.avatarUrl ? (
                          <img src={shopProfile.avatarUrl} alt={shopProfile.name} className="w-10 h-10 rounded-full object-cover ring-2 ring-white/50"/>
                      ) : (
                          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${hasBanner ? 'bg-white/20' : 'bg-accent-light'}`}>
                              <User className={`w-6 h-6 ${hasBanner ? 'text-white' : 'text-accent'}`}/>
                          </div>
                      )}
                      <h1 className="text-xl font-bold">{shopProfile.name}</h1>
                  </div>
                  ) : (
                  <div className="flex items-center gap-2 text-gray-800">
                      <button onClick={goBack} className="p-2 -ml-2 rounded-full hover:bg-gray-100">
                      <ArrowLeft size={24} />
                      </button>
                      <h1 className="text-xl font-bold">{title}</h1>
                  </div>
                  )}
                  
                  {showSettingsAndNotifications && <Icons />}
              </div>
              
              {showSearchBar && (
                   <form key={screen} onSubmit={handleSearchSubmit} className={`relative ${hasBanner ? '' : 'mt-4 px-4 pb-4'}`}>
                      <div className={`pointer-events-none absolute top-1/2 -translate-y-1/2 left-3 flex items-center gap-2 ${hasBanner ? 'text-gray-200' : 'text-gray-400 ml-4'}`}>
                          {screen === 'Home' ? <GoogleIcon /> : <Search size={20} />}
                      </div>
                      <input
                          ref={searchInputRef}
                          type="text"
                          placeholder={placeholder}
                          className={`w-full border rounded-lg py-2.5 pl-12 focus:outline-none focus:ring-2 focus:ring-accent/50 transition-all ${
                              screen === 'Home' ? 'pr-24' : 'pr-4'
                          } ${
                              hasBanner 
                              ? 'bg-white/20 border-white/30 placeholder-gray-200 focus:bg-white/30' 
                              : 'bg-gray-100 border-gray-200 placeholder-gray-500'
                          }`}
                      />
                       {screen === 'Home' && (
                          <div className={`absolute top-1/2 -translate-y-1/2 flex items-center gap-1 ${hasBanner ? 'right-2' : 'right-6'}`}>
                              <button
                                  type="button"
                                  onClick={() => {
                                      if (hasRecognitionSupport) startListening();
                                      else setToast('Voice search is not supported by your browser.');
                                  }}
                                  className={`p-2 rounded-full transition-colors ${hasBanner ? 'text-gray-200 hover:bg-white/20' : 'text-gray-500 hover:bg-gray-200'} ${isListening ? 'bg-accent/20 text-accent animate-pulse' : ''}`}
                                  aria-label="Voice Search"
                              >
                                  <Mic size={20} />
                              </button>
                               <button
                                  type="button"
                                  onClick={() => setIsScannerOpen(true)}
                                  className={`p-2 rounded-full transition-colors ${hasBanner ? 'text-gray-200 hover:bg-white/20' : 'text-gray-500 hover:bg-gray-200'}`}
                                  aria-label="Scan Code"
                              >
                                  <QrCode size={20} />
                              </button>
                          </div>
                      )}
                  </form>
              )}
          </div>
        </div>
      </header>
    </>
  );
};

export default TopBar;