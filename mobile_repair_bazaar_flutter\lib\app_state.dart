import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'models.dart';

class AppState extends ChangeNotifier {
  // Keys
  static const _ordersKey = 'app_orders';
  static const _customersKey = 'app_customers';
  static const _inventoryKey = 'app_inventory';
  static const _wholesaleKey = 'app_wholesale';
  static const _techniciansKey = 'app_technicians';
  static const _shopProfileKey = 'app_shop_profile';
  static const _userProfileKey = 'app_user_profile';
  static const _appSettingsKey = 'app_settings';

  // Data
  List<Order> orders = [];
  List<Customer> customers = [];
  List<InventoryItem> inventory = [];
  List<WholesaleItem> wholesaleInventory = [];
  List<Technician> technicians = [];
  ShopProfile shopProfile = const ShopProfile(name: 'Your Repair Shop');
  UserProfile? userProfile;
  AppSettings appSettings = const AppSettings();

  bool _loaded = false;
  bool get isLoaded => _loaded;

  Future<void> load() async {
    final prefs = await SharedPreferences.getInstance();
    try {
      orders = _readList(prefs, _ordersKey, (m) => Order.fromJson(m));
      customers = _readList(prefs, _customersKey, (m) => Customer.fromJson(m));
      inventory = _readList(prefs, _inventoryKey, (m) => InventoryItem.fromJson(m));
      wholesaleInventory = _readList(prefs, _wholesaleKey, (m) => WholesaleItem.fromJson(m));
      technicians = _readList(prefs, _techniciansKey, (m) => Technician.fromJson(m));

      final shopRaw = prefs.getString(_shopProfileKey);
      if (shopRaw != null) {
        shopProfile = ShopProfile.fromJson(jsonDecode(shopRaw));
      }

      final userProfileRaw = prefs.getString(_userProfileKey);
      if (userProfileRaw != null) {
        userProfile = UserProfile.fromJson(jsonDecode(userProfileRaw));
      }

      final appSettingsRaw = prefs.getString(_appSettingsKey);
      if (appSettingsRaw != null) {
        appSettings = AppSettings.fromJson(jsonDecode(appSettingsRaw));
      }
    } catch (_) {
      // Swallow malformed data
    } finally {
      _loaded = true;
      notifyListeners();
    }
  }

  // Helpers
  List<T> _readList<T>(SharedPreferences prefs, String key, T Function(Map<String, dynamic>) fromJson) {
    final raw = prefs.getString(key);
    if (raw == null || raw.isEmpty) return [];
    final list = (jsonDecode(raw) as List).cast<dynamic>();
    return list.map((e) => fromJson((e as Map).cast<String, dynamic>())).toList();
  }

  Future<void> _writeList(String key, List<Map<String, dynamic>> list) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(key, jsonEncode(list));
  }

  // Mutations (minimal for demo parity)
  Future<void> addInventoryItem(InventoryItem item) async {
    inventory = [item, ...inventory];
    await _writeList(_inventoryKey, inventory.map((e) => e.toJson()).toList());
    notifyListeners();
  }

  Future<void> updateInventoryItem(InventoryItem updatedItem) async {
    final idx = inventory.indexWhere((item) => item.id == updatedItem.id);
    if (idx == -1) return;
    inventory = [...inventory]..[idx] = updatedItem.copyWith(updatedAt: DateTime.now());
    await _writeList(_inventoryKey, inventory.map((e) => e.toJson()).toList());
    notifyListeners();
  }

  Future<void> deleteInventoryItem(String id) async {
    inventory = inventory.where((item) => item.id != id).toList();
    await _writeList(_inventoryKey, inventory.map((e) => e.toJson()).toList());
    notifyListeners();
  }

  Future<void> updateInventoryQuantity(String id, int newQuantity) async {
    final idx = inventory.indexWhere((item) => item.id == id);
    if (idx == -1) return;
    final updatedItem = inventory[idx].copyWith(
      quantity: newQuantity,
      updatedAt: DateTime.now(),
    );
    inventory = [...inventory]..[idx] = updatedItem;
    await _writeList(_inventoryKey, inventory.map((e) => e.toJson()).toList());
    notifyListeners();
  }

  Future<void> adjustInventoryStock(String id, int adjustment, String reason) async {
    final idx = inventory.indexWhere((item) => item.id == id);
    if (idx == -1) return;
    final currentItem = inventory[idx];
    final newQuantity = (currentItem.quantity + adjustment).clamp(0, double.infinity).toInt();
    await updateInventoryQuantity(id, newQuantity);
  }

  List<InventoryItem> get lowStockItems => inventory.where((item) => item.isLowStock).toList();
  List<InventoryItem> get outOfStockItems => inventory.where((item) => item.isOutOfStock).toList();

  List<InventoryItem> getInventoryByCategory(InventoryCategory category) {
    return inventory.where((item) => item.category == category).toList();
  }

  InventoryItem? getInventoryItemById(String id) {
    try {
      return inventory.firstWhere((item) => item.id == id);
    } catch (e) {
      return null;
    }
  }

  double get totalInventoryValue => inventory.fold(0.0, (sum, item) => sum + (item.costPrice * item.quantity));
  double get totalInventorySellingValue => inventory.fold(0.0, (sum, item) => sum + (item.sellingPrice * item.quantity));
  double get totalInventoryProfit => totalInventorySellingValue - totalInventoryValue;

  Future<void> addCustomer(Customer customer) async {
    customers = [customer, ...customers];
    await _writeList(_customersKey, customers.map((e) => e.toJson()).toList());
    notifyListeners();
  }

  Future<Order> createJobCard({
    required String id,
    required String customerName,
    required String customerPhone,
    String? customerId,
    String? customerAddress,
    String? brand,
    required String deviceModel,
    String? imei,
    String? serialNo,
    required String issueDescription,
    required double estimatedCost,
    required double advancePayment,
    required JobPriority priority,
    String? photoUrl,
    List<String> photoUrls = const [],
    String? technicianId,
    List<UsedPart> usedParts = const [],
    double laborCost = 0,
  }) async {
    // Ensure customer: prefer by id, then by phone, else create new
    Customer? customer;
    if (customerId != null) {
      try {
        customer = customers.firstWhere((c) => c.id == customerId);
      } catch (_) {
        customer = null;
      }
    }
    customer ??= (() {
      try {
        return customers.firstWhere((c) => c.phone == customerPhone);
      } catch (_) {
        return null;
      }
    })();
    customer ??= Customer(
      id: 'CUS-${DateTime.now().millisecondsSinceEpoch}',
      name: customerName,
      phone: customerPhone,
      address: customerAddress,
    );
    if (!customers.any((c) => c.id == customer?.id)) {
      await addCustomer(customer!);
    }

    final now = DateTime.now();
    final order = Order(
      id: id,
      customer: customer,
      brand: brand,
      deviceModel: deviceModel,
      imei: imei,
      serialNo: serialNo,
      issueDescription: issueDescription,
      estimatedCost: estimatedCost,
      advancePayment: advancePayment,
      status: OrderStatus.received,
      priority: priority,
      createdAt: now,
      updatedAt: now,
      photoUrl: photoUrl,
      photoUrls: photoUrls,
      technicianId: technicianId,
      usedParts: usedParts,
      laborCost: laborCost,
    );

    orders = [order, ...orders];
    await _writeList(_ordersKey, orders.map((e) => e.toJson()).toList());
    notifyListeners();
    return order;
  }

  Order? getOrderById(String id) {
    try {
      return orders.firstWhere((o) => o.id == id);
    } catch (_) {
      return null;
    }
  }

  Future<void> updateOrderStatus(String id, OrderStatus status) async {
    final idx = orders.indexWhere((o) => o.id == id);
    if (idx == -1) return;
    final current = orders[idx];
    final updated = Order(
      id: current.id,
      customer: current.customer,
      brand: current.brand,
      deviceModel: current.deviceModel,
      imei: current.imei,
      serialNo: current.serialNo,
      issueDescription: current.issueDescription,
      estimatedCost: current.estimatedCost,
      advancePayment: current.advancePayment,
      status: status,
      priority: current.priority,
      createdAt: current.createdAt,
      updatedAt: DateTime.now(),
      photoUrl: current.photoUrl,
      technicianId: current.technicianId,
      usedParts: current.usedParts,
      laborCost: current.laborCost,
    );
    orders = [...orders]..[idx] = updated;
    await _writeList(_ordersKey, orders.map((e) => e.toJson()).toList());
    notifyListeners();
  }

  Future<void> updateCustomer(Customer customer) async {
    final idx = customers.indexWhere((c) => c.id == customer.id);
    if (idx == -1) return;
    customers = [...customers]..[idx] = customer;
    await _writeList(_customersKey, customers.map((e) => e.toJson()).toList());
    notifyListeners();
  }

  Future<void> deleteOrder(String id) async {
    orders = orders.where((o) => o.id != id).toList();
    await _writeList(_ordersKey, orders.map((e) => e.toJson()).toList());
    notifyListeners();
  }

  Future<void> updateOrder(Order updatedOrder) async {
    final idx = orders.indexWhere((o) => o.id == updatedOrder.id);
    if (idx == -1) return;
    orders = [...orders]..[idx] = updatedOrder;
    await _writeList(_ordersKey, orders.map((e) => e.toJson()).toList());
    notifyListeners();
  }

  // User Profile Management
  Future<void> updateUserProfile(UserProfile profile) async {
    userProfile = profile.copyWith(updatedAt: DateTime.now());
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userProfileKey, jsonEncode(userProfile!.toJson()));
    notifyListeners();
  }

  Future<void> createUserProfile({
    required String name,
    String? email,
    String? phone,
    String? businessName,
    String? businessAddress,
    String? businessPhone,
    String? businessEmail,
    String? gstNumber,
    String? profileImagePath,
    String? businessLogoPath,
    Map<String, String>? businessHours,
  }) async {
    final now = DateTime.now();
    final profile = UserProfile(
      id: 'USER-${now.millisecondsSinceEpoch}',
      name: name,
      email: email,
      phone: phone,
      businessName: businessName,
      businessAddress: businessAddress,
      businessPhone: businessPhone,
      businessEmail: businessEmail,
      gstNumber: gstNumber,
      profileImagePath: profileImagePath,
      businessLogoPath: businessLogoPath,
      businessHours: businessHours,
      createdAt: now,
      updatedAt: now,
    );
    await updateUserProfile(profile);
  }

  // App Settings Management
  Future<void> updateAppSettings(AppSettings settings) async {
    appSettings = settings;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_appSettingsKey, jsonEncode(appSettings.toJson()));
    notifyListeners();
  }

  Future<void> updateSetting<T>(String key, T value) async {
    AppSettings newSettings;
    switch (key) {
      case 'isDarkMode':
        newSettings = appSettings.copyWith(isDarkMode: value as bool);
        break;
      case 'language':
        newSettings = appSettings.copyWith(language: value as String);
        break;
      case 'currency':
        newSettings = appSettings.copyWith(currency: value as String);
        break;
      case 'notificationsEnabled':
        newSettings = appSettings.copyWith(notificationsEnabled: value as bool);
        break;
      case 'autoBackup':
        newSettings = appSettings.copyWith(autoBackup: value as bool);
        break;
      case 'dateFormat':
        newSettings = appSettings.copyWith(dateFormat: value as String);
        break;
      case 'timeFormat':
        newSettings = appSettings.copyWith(timeFormat: value as String);
        break;
      case 'taxRate':
        newSettings = appSettings.copyWith(taxRate: value as double);
        break;
      case 'biometricEnabled':
        newSettings = appSettings.copyWith(biometricEnabled: value as bool);
        break;
      case 'autoLogoutMinutes':
        newSettings = appSettings.copyWith(autoLogoutMinutes: value as int);
        break;
      default:
        return;
    }
    await updateAppSettings(newSettings);
  }

  // Data Management
  Future<void> exportData() async {
    // TODO: Implement data export functionality
  }

  Future<void> importData(String data) async {
    // TODO: Implement data import functionality
  }

  Future<void> backupData() async {
    // TODO: Implement backup functionality
  }

  Future<void> restoreData(String backupData) async {
    // TODO: Implement restore functionality
  }

  Future<void> clearAllData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
    orders = [];
    customers = [];
    inventory = [];
    wholesaleInventory = [];
    technicians = [];
    shopProfile = const ShopProfile(name: 'Your Repair Shop');
    userProfile = null;
    appSettings = const AppSettings();
    notifyListeners();
  }
}


