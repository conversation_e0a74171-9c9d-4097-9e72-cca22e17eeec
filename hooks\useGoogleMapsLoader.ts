import { useState, useEffect } from 'react';

// Use a global variable to ensure the script is only loaded once,
// and to manage listeners waiting for the script.
let isScriptLoaded = false;
let isScriptLoading = false;
const listeners: ((loaded: boolean) => void)[] = [];

const GOOGLE_MAPS_CALLBACK_NAME = 'initGoogleMaps';

// Define the callback function on the window object
if (typeof window !== 'undefined') {
  (window as any)[GOOGLE_MAPS_CALLBACK_NAME] = () => {
    isScriptLoaded = true;
    isScriptLoading = false;
    listeners.forEach(listener => listener(true));
    listeners.length = 0; // Clear listeners after calling them
  };
}

const useGoogleMapsLoader = () => {
  const [isLoaded, setIsLoaded] = useState(isScriptLoaded);

  useEffect(() => {
    if (isScriptLoaded) {
      setIsLoaded(true);
      return;
    }
    
    // Add the setter to the listeners queue
    listeners.push(setIsLoaded);

    if (!isScriptLoading) {
        isScriptLoading = true;
        
        const apiKey = process.env.API_KEY;
        if (!apiKey) {
            console.error("API_KEY is not set. This key is required for Google Maps functionality.");
            isScriptLoading = false; // Reset loading state on error
            listeners.forEach(listener => listener(false));
            listeners.length = 0;
            return;
        }

        const script = document.createElement('script');
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=${GOOGLE_MAPS_CALLBACK_NAME}`;
        script.async = true;
        script.onerror = () => {
             console.error("Failed to load Google Maps script. Check if the API_KEY is valid and has the 'Maps JavaScript API' and 'Places API' enabled.");
             isScriptLoading = false;
             listeners.forEach(listener => listener(false));
             listeners.length = 0;
        }
        document.head.appendChild(script);
    }
    
    return () => {
        // Cleanup listener on component unmount
        const index = listeners.indexOf(setIsLoaded);
        if (index > -1) {
            listeners.splice(index, 1);
        }
    };
  }, []);

  return isLoaded;
};

export default useGoogleMapsLoader;