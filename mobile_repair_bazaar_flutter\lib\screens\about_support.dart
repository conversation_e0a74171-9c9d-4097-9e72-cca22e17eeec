import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutSupportScreen extends StatelessWidget {
  final bool showAbout;
  
  const AboutSupportScreen({super.key, this.showAbout = false});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.black, Color(0xFF0A0A0A)],
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          title: Text(
            showAbout ? 'About App' : 'Help & Support',
            style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
          ),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (showAbout) ...[
                // App Info
                Center(
                  child: Column(
                    children: [
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: const Color(0xFFE50914),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Icon(Icons.build, color: Colors.white, size: 40),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Mobile Repair Bazaar',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Version 1.0.0',
                        style: TextStyle(color: Colors.white70, fontSize: 16),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        'Professional Mobile Repair Management',
                        style: TextStyle(color: Colors.white54, fontSize: 14),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 32),
                
                _buildInfoCard('App Information', [
                  _InfoItem('Version', '1.0.0'),
                  _InfoItem('Build', '100'),
                  _InfoItem('Developer', 'Mobile Repair Solutions'),
                  _InfoItem('Release Date', 'December 2024'),
                ]),
                
                const SizedBox(height: 20),
                
                _buildInfoCard('Legal', [
                  _InfoItem('Privacy Policy', 'View our privacy policy', onTap: () {}),
                  _InfoItem('Terms of Service', 'Read terms and conditions', onTap: () {}),
                  _InfoItem('Licenses', 'Open source licenses', onTap: () {}),
                ]),
              ] else ...[
                // Help & Support
                _buildInfoCard('Get Help', [
                  _InfoItem('FAQ', 'Frequently asked questions', onTap: () {}),
                  _InfoItem('User Guide', 'Learn how to use the app', onTap: () {}),
                  _InfoItem('Video Tutorials', 'Watch helpful tutorials', onTap: () {}),
                ]),
                
                const SizedBox(height: 20),
                
                _buildInfoCard('Contact Support', [
                  _InfoItem('Email Support', '<EMAIL>', onTap: () => _launchEmail()),
                  _InfoItem('Phone Support', '+91 **********', onTap: () => _launchPhone()),
                  _InfoItem('WhatsApp', 'Chat with us on WhatsApp', onTap: () => _launchWhatsApp()),
                ]),
                
                const SizedBox(height: 20),
                
                _buildInfoCard('Feedback', [
                  _InfoItem('Rate App', 'Rate us on Play Store', onTap: () {}),
                  _InfoItem('Send Feedback', 'Share your thoughts', onTap: () {}),
                  _InfoItem('Report Bug', 'Report issues or bugs', onTap: () {}),
                ]),
              ],
              
              const SizedBox(height: 32),
              
              // Footer
              const Center(
                child: Text(
                  '© 2024 Mobile Repair Bazaar\nAll rights reserved',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.white54, fontSize: 12),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard(String title, List<_InfoItem> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFF1F1F1F),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.white12),
          ),
          child: Column(
            children: items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final isLast = index == items.length - 1;
              
              return Column(
                children: [
                  ListTile(
                    title: Text(
                      item.title,
                      style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w500),
                    ),
                    subtitle: item.subtitle != null
                        ? Text(
                            item.subtitle!,
                            style: const TextStyle(color: Colors.white70, fontSize: 12),
                          )
                        : null,
                    trailing: item.onTap != null
                        ? const Icon(Icons.chevron_right, color: Colors.white54)
                        : Text(
                            item.value ?? '',
                            style: const TextStyle(color: Colors.white70),
                          ),
                    onTap: item.onTap,
                  ),
                  if (!isLast)
                    const Divider(color: Colors.white12, height: 1),
                ],
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  void _launchEmail() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      query: 'subject=Support Request',
    );
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    }
  }

  void _launchPhone() async {
    final Uri phoneUri = Uri(scheme: 'tel', path: '+91**********');
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    }
  }

  void _launchWhatsApp() async {
    final Uri whatsappUri = Uri.parse('https://wa.me/91**********');
    if (await canLaunchUrl(whatsappUri)) {
      await launchUrl(whatsappUri);
    }
  }
}

class _InfoItem {
  final String title;
  final String? subtitle;
  final String? value;
  final VoidCallback? onTap;

  const _InfoItem(this.title, this.subtitle, {this.value, this.onTap});
}
