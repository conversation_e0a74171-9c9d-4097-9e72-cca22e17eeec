import React from 'react';

interface SwitchProps {
  enabled: boolean;
  onChange: (enabled: boolean) => void;
  'aria-label': string;
}

const Switch: React.FC<SwitchProps> = ({ enabled, onChange, 'aria-label': ariaLabel }) => {
  const handleToggle = () => {
    onChange(!enabled);
  };

  return (
    <button
      type="button"
      onClick={handleToggle}
      className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 focus:ring-offset-white ${
        enabled ? 'bg-accent' : 'bg-gray-300'
      }`}
      role="switch"
      aria-checked={enabled}
      aria-label={ariaLabel}
    >
      <span
        aria-hidden="true"
        className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-lg ring-0 transition-transform duration-300 ease-in-out ${
          enabled ? 'translate-x-5' : 'translate-x-0'
        }`}
      />
    </button>
  );
};

export default Switch;
