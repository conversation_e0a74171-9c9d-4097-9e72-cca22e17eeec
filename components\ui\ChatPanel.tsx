import React, { useState, useEffect, useRef } from 'react';
import { Send, X, Bot, User, Loader2 } from 'lucide-react';
import { useApp } from '../../context/AppContext';
import Button from './Button';

interface ChatPanelProps {
    isOpen: boolean;
    onClose: () => void;
}

const TypingIndicator = () => (
    <div className="flex items-center gap-1.5">
        <span className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.3s]"></span>
        <span className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.15s]"></span>
        <span className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></span>
    </div>
);

const ChatPanel: React.FC<ChatPanelProps> = ({ isOpen, onClose }) => {
    const { chatHistory, isAiResponding, sendMessageToAi } = useApp();
    const [inputMessage, setInputMessage] = useState('');
    const messagesEndRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'auto';
        }
        return () => {
            document.body.style.overflow = 'auto';
        };
    }, [isOpen]);

    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [chatHistory, isAiResponding]);

    const handleSend = (e: React.FormEvent) => {
        e.preventDefault();
        const message = inputMessage.trim();
        if (message && !isAiResponding) {
            sendMessageToAi(message);
            setInputMessage('');
        }
    };
    
    if (!isOpen) return null;

    return (
        <div role="dialog" aria-modal="true" className="fixed inset-0 z-40">
            {/* Backdrop */}
            <div className="absolute inset-0 bg-black/30" onClick={onClose}></div>

            {/* Panel */}
            <div className="absolute top-0 right-0 h-full w-full max-w-md bg-white shadow-xl flex flex-col animate-slide-in-right">
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-200">
                    <div className="flex items-center gap-3">
                        <Bot size={24} className="text-accent" />
                        <h2 className="text-lg font-bold text-gray-800">AI Assistant</h2>
                    </div>
                    <button onClick={onClose} className="p-2 rounded-full text-gray-500 hover:bg-gray-100">
                        <X size={24} />
                    </button>
                </div>

                {/* Messages */}
                <div className="flex-grow p-4 overflow-y-auto">
                    <div className="space-y-6">
                        {chatHistory.map((msg, index) => (
                            <div key={index} className={`flex items-start gap-3 ${msg.role === 'user' ? 'justify-end' : ''}`}>
                                {msg.role === 'model' && (
                                    <div className="w-8 h-8 rounded-full bg-accent-light flex items-center justify-center flex-shrink-0">
                                        <Bot size={20} className="text-accent"/>
                                    </div>
                                )}
                                <div className={`max-w-xs md:max-w-sm px-4 py-2.5 rounded-2xl ${msg.role === 'user' ? 'bg-accent text-white rounded-br-lg' : 'bg-gray-100 text-gray-800 rounded-bl-lg'}`}>
                                    <p className="text-sm leading-relaxed whitespace-pre-wrap">{msg.text}</p>
                                </div>
                                 {msg.role === 'user' && (
                                    <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center flex-shrink-0">
                                        <User size={18} className="text-gray-600"/>
                                    </div>
                                )}
                            </div>
                        ))}
                        {isAiResponding && chatHistory[chatHistory.length - 1]?.role !== 'model' && (
                             <div className="flex items-start gap-3">
                                <div className="w-8 h-8 rounded-full bg-accent-light flex items-center justify-center flex-shrink-0">
                                    <Bot size={20} className="text-accent"/>
                                </div>
                                <div className="px-4 py-2.5 rounded-2xl bg-gray-100 text-gray-800 rounded-bl-lg">
                                    <TypingIndicator />
                                </div>
                            </div>
                        )}
                        <div ref={messagesEndRef} />
                    </div>
                </div>

                {/* Input Form */}
                <div className="p-4 border-t border-gray-200 bg-white">
                    <form onSubmit={handleSend} className="flex items-center gap-3">
                        <input
                            type="text"
                            value={inputMessage}
                            onChange={(e) => setInputMessage(e.target.value)}
                            placeholder="Ask about your shop..."
                            className="w-full bg-gray-100 border-gray-200 rounded-full py-2.5 px-4 focus:outline-none focus:ring-2 focus:ring-accent/50"
                            disabled={isAiResponding}
                        />
                        <Button type="submit" disabled={isAiResponding || !inputMessage.trim()} className="!rounded-full !p-3">
                            {isAiResponding ? <Loader2 size={20} className="animate-spin" /> : <Send size={20} />}
                        </Button>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default ChatPanel;
