import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'app_state.dart';
import 'models.dart';
import 'screens/orders.dart';
import 'screens/new_order.dart';
import 'screens/customers.dart';
import 'screens/add_customer.dart';
import 'screens/inventory.dart';
import 'screens/settings.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => AppState()..load(),
      child: Consumer<AppState>(
        builder: (context, appState, _) {
          return MaterialApp(
            title: 'Mobile Repair Bazaar',
            theme: appState.appSettings.isDarkMode ? _darkTheme : _lightTheme,
        home: const RootScaffold(),
        // Basic routes. For advanced flows (like return-to-new after add), use onGenerateRoute below.
        routes: {
          '/orders': (_) => const OrdersScreen(),
          '/new': (_) => const NewOrderScreen(),
          '/add_customer': (_) => const AddCustomerScreen(),
        },
        onGenerateRoute: (settings) {
          if (settings.name == '/new' && settings.arguments is Customer) {
            final c = settings.arguments as Customer;
            return MaterialPageRoute(builder: (_) => NewOrderScreen(initialCustomer: c));
          }
          return null;
        },
          );
        },
      ),
    );
  }

  ThemeData get _darkTheme => ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    scaffoldBackgroundColor: const Color(0xFF18191A), // Facebook dark background
    colorScheme: const ColorScheme.dark().copyWith(
      primary: const Color(0xFF1877F2), // Facebook blue
      secondary: const Color(0xFF42A5F5), // Facebook light blue
      tertiary: const Color(0xFFFF9500), // Facebook orange
      surface: const Color(0xFF242526), // Facebook dark surface
      background: const Color(0xFF18191A), // Facebook dark background
      onSurface: const Color(0xFFE4E6EA), // Facebook dark text
      onBackground: const Color(0xFFE4E6EA), // Facebook dark text
      onPrimary: Colors.white,
      error: const Color(0xFFE74C3C), // Facebook red
      outline: const Color(0xFF3E4042), // Facebook dark border
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: Color(0xFF242526), // Facebook dark surface
      foregroundColor: Color(0xFFE4E6EA), // Facebook dark text
      elevation: 0,
      surfaceTintColor: Colors.transparent,
      titleTextStyle: TextStyle(
        color: Color(0xFFE4E6EA),
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: Color(0xFF242526), // Facebook dark surface
      selectedItemColor: Color(0xFF1877F2), // Facebook blue
      unselectedItemColor: Color(0xFFB0B3B8), // Facebook secondary text
      type: BottomNavigationBarType.fixed,
      elevation: 8,
    ),
    cardTheme: CardThemeData(
      color: const Color(0xFF3A3B3C), // Facebook dark card
      elevation: 2,
      shadowColor: Colors.black.withOpacity(0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12), // Facebook card radius
        side: const BorderSide(color: Color(0xFF3E4042), width: 1), // Facebook border
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: const Color(0xFF242526), // Facebook dark surface
      labelStyle: const TextStyle(color: Color(0xFFB0B3B8)), // Facebook secondary text
      hintStyle: const TextStyle(color: Color(0xFFB0B3B8)), // Facebook secondary text
      prefixIconColor: const Color(0xFF1877F2), // Facebook blue
      suffixIconColor: const Color(0xFFB0B3B8), // Facebook secondary text
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8), // Facebook radius
        borderSide: const BorderSide(color: Color(0xFF3E4042)), // Facebook border
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8), // Facebook radius
        borderSide: const BorderSide(color: Color(0xFF1877F2), width: 2), // Facebook blue
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8), // Facebook radius
        borderSide: const BorderSide(color: Color(0xFFE74C3C)), // Facebook red
      ),
    ),
    filledButtonTheme: FilledButtonThemeData(
      style: FilledButton.styleFrom(
        backgroundColor: const Color(0xFF1877F2), // Facebook blue
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)), // Facebook button radius
        elevation: 1,
        textStyle: const TextStyle(fontWeight: FontWeight.w600, fontSize: 15),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: const Color(0xFF1877F2), // Facebook blue
        side: const BorderSide(color: Color(0xFF1877F2)), // Facebook blue border
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)), // Facebook button radius
        textStyle: const TextStyle(fontWeight: FontWeight.w600, fontSize: 15),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xFF242526), // Facebook dark surface
        foregroundColor: const Color(0xFFE4E6EA), // Facebook dark text
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)), // Facebook button radius
        elevation: 1,
        textStyle: const TextStyle(fontWeight: FontWeight.w600, fontSize: 15),
      ),
    ),
    textTheme: const TextTheme(
      headlineLarge: TextStyle(color: Color(0xFFE4E6EA), fontWeight: FontWeight.bold, fontSize: 28),
      headlineMedium: TextStyle(color: Color(0xFFE4E6EA), fontWeight: FontWeight.w600, fontSize: 24),
      titleLarge: TextStyle(color: Color(0xFFE4E6EA), fontWeight: FontWeight.w600, fontSize: 20),
      titleMedium: TextStyle(color: Color(0xFFE4E6EA), fontWeight: FontWeight.w600, fontSize: 16),
      bodyLarge: TextStyle(color: Color(0xFFE4E6EA), fontSize: 16),
      bodyMedium: TextStyle(color: Color(0xFFB0B3B8), fontSize: 14),
      labelLarge: TextStyle(color: Color(0xFF1877F2), fontWeight: FontWeight.w600, fontSize: 14),
    ),
  );

  ThemeData get _lightTheme => ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    scaffoldBackgroundColor: const Color(0xFFF0F2F5), // Facebook light background
    colorScheme: const ColorScheme.light().copyWith(
      primary: const Color(0xFF1877F2), // Facebook blue
      secondary: const Color(0xFF42A5F5), // Facebook light blue
      tertiary: const Color(0xFFFF9500), // Facebook orange
      surface: Colors.white, // Facebook light surface
      background: const Color(0xFFF0F2F5), // Facebook light background
      onSurface: const Color(0xFF1C1E21), // Facebook light text
      onBackground: const Color(0xFF1C1E21), // Facebook light text
      onPrimary: Colors.white,
      error: const Color(0xFFE74C3C), // Facebook red
      outline: const Color(0xFFDADDE1), // Facebook light border
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.white,
      foregroundColor: Colors.black,
      elevation: 1,
      surfaceTintColor: Colors.transparent,
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: Colors.white,
      selectedItemColor: Color(0xFFE50914),
      unselectedItemColor: Colors.grey,
      type: BottomNavigationBarType.fixed,
    ),
    cardTheme: CardThemeData(
      color: Colors.white,
      elevation: 2,
      shadowColor: Colors.grey.withOpacity(0.3),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: Colors.grey[50],
      labelStyle: const TextStyle(color: Colors.black87),
      hintStyle: const TextStyle(color: Colors.black54),
      prefixIconColor: Colors.black54,
      suffixIconColor: Colors.black54,
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: Colors.grey),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: Color(0xFFE50914)),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: Colors.red),
      ),
    ),
    filledButtonTheme: FilledButtonThemeData(
      style: FilledButton.styleFrom(
        backgroundColor: const Color(0xFF1877F2), // Facebook blue
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)), // Facebook button radius
        elevation: 1,
        textStyle: const TextStyle(fontWeight: FontWeight.w600, fontSize: 15),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: const Color(0xFF1877F2), // Facebook blue
        side: const BorderSide(color: Color(0xFF1877F2)), // Facebook blue border
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)), // Facebook button radius
        textStyle: const TextStyle(fontWeight: FontWeight.w600, fontSize: 15),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xFFF0F2F5), // Facebook light surface
        foregroundColor: const Color(0xFF1C1E21), // Facebook light text
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)), // Facebook button radius
        elevation: 1,
        textStyle: const TextStyle(fontWeight: FontWeight.w600, fontSize: 15),
      ),
    ),
    textTheme: const TextTheme(
      headlineLarge: TextStyle(color: Color(0xFF1C1E21), fontWeight: FontWeight.bold, fontSize: 28),
      headlineMedium: TextStyle(color: Color(0xFF1C1E21), fontWeight: FontWeight.w600, fontSize: 24),
      titleLarge: TextStyle(color: Color(0xFF1C1E21), fontWeight: FontWeight.w600, fontSize: 20),
      titleMedium: TextStyle(color: Color(0xFF1C1E21), fontWeight: FontWeight.w600, fontSize: 16),
      bodyLarge: TextStyle(color: Color(0xFF1C1E21), fontSize: 16),
      bodyMedium: TextStyle(color: Color(0xFF65676B), fontSize: 14),
      labelLarge: TextStyle(color: Color(0xFF1877F2), fontWeight: FontWeight.w600, fontSize: 14),
    ),
  );
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  // This widget is the home page of your application. It is stateful, meaning
  // that it has a State object (defined below) that contains fields that affect
  // how it looks.

  // This class is the configuration for the state. It holds the values (in this
  // case the title) provided by the parent (in this case the App widget) and
  // used by the build method of the State. Fields in a Widget subclass are
  // always marked "final".

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int _counter = 0;

  void _incrementCounter() {
    setState(() {
      // This call to setState tells the Flutter framework that something has
      // changed in this State, which causes it to rerun the build method below
      // so that the display can reflect the updated values. If we changed
      // _counter without calling setState(), then the build method would not be
      // called again, and so nothing would appear to happen.
      _counter++;
    });
  }

  @override
  Widget build(BuildContext context) {
    // This method is rerun every time setState is called, for instance as done
    // by the _incrementCounter method above.
    //
    // The Flutter framework has been optimized to make rerunning build methods
    // fast, so that you can just rebuild anything that needs updating rather
    // than having to individually change instances of widgets.
    return Scaffold(
      appBar: AppBar(
        // TRY THIS: Try changing the color here to a specific color (to
        // Colors.amber, perhaps?) and trigger a hot reload to see the AppBar
        // change color while the other colors stay the same.
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        // Here we take the value from the MyHomePage object that was created by
        // the App.build method, and use it to set our appbar title.
        title: Text(widget.title),
      ),
      body: Center(
        // Center is a layout widget. It takes a single child and positions it
        // in the middle of the parent.
        child: Column(
          // Column is also a layout widget. It takes a list of children and
          // arranges them vertically. By default, it sizes itself to fit its
          // children horizontally, and tries to be as tall as its parent.
          //
          // Column has various properties to control how it sizes itself and
          // how it positions its children. Here we use mainAxisAlignment to
          // center the children vertically; the main axis here is the vertical
          // axis because Columns are vertical (the cross axis would be
          // horizontal).
          //
          // TRY THIS: Invoke "debug painting" (choose the "Toggle Debug Paint"
          // action in the IDE, or press "p" in the console), to see the
          // wireframe for each widget.
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Text('You have pushed the button this many times:'),
            Text(
              '$_counter',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _incrementCounter,
        tooltip: 'Increment',
        child: const Icon(Icons.add),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }
}

// New scaffold with bottom navigation and dashboard placeholders
class RootScaffold extends StatefulWidget {
  const RootScaffold({super.key});

  @override
  State<RootScaffold> createState() => _RootScaffoldState();
}

class _RootScaffoldState extends State<RootScaffold> {
  int _index = 0;

  static final List<_NavItem> _items = [
    _NavItem('Home', Icons.home_rounded, const _HomeScreen()),
    _NavItem('Orders', Icons.build_rounded, const OrdersScreen()),
    _NavItem('Inventory', Icons.inventory_2_rounded, const InventoryScreen()),
    _NavItem('Customers', Icons.group_rounded, const CustomersScreen()),
    _NavItem('Settings', Icons.settings_rounded, const SettingsScreen()),
  ];

  @override
  Widget build(BuildContext context) {
    final current = _items[_index];
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.black, Color(0xFF0A0A0A)],
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          title: Text(current.title, style: const TextStyle(fontWeight: FontWeight.bold)),
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: Builder(
            builder: (context) => IconButton(
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFE50914),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.menu, color: Colors.white, size: 20),
              ),
              onPressed: () => Scaffold.of(context).openDrawer(),
            ),
          ),
        ),
        body: current.screen,
        bottomNavigationBar: Container(
          height: 60,
          decoration: const BoxDecoration(
            color: Color(0xFF121212),
            border: Border(top: BorderSide(color: Colors.white12)),
          ),
          child: Row(
            children: [
              for (int i = 0; i < _items.length; i++)
                Expanded(
                  child: InkWell(
                    onTap: () => setState(() => _index = i),
                    child: Container(
                      height: 60,
                      decoration: BoxDecoration(
                        color: _index == i ? const Color(0xFFE50914) : Colors.transparent,
                      ),
                      child: Center(
                        child: Icon(
                          _items[i].icon,
                          color: _index == i ? Colors.white : Colors.white54,
                          size: 24,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
        drawer: Drawer(
          backgroundColor: const Color(0xFF121212),
          child: SafeArea(
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Color(0xFFE50914), Color(0xFFB20710)],
                    ),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.build_circle, color: Colors.white, size: 32),
                      SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Mobile Repair', style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold)),
                          Text('Bazaar', style: TextStyle(color: Colors.white70, fontSize: 14)),
                        ],
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: ListView(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    children: [
                      for (int i = 0; i < _items.length; i++)
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: _index == i ? const Color(0xFFE50914).withOpacity(0.1) : Colors.transparent,
                            borderRadius: BorderRadius.circular(12),
                            border: _index == i ? Border.all(color: const Color(0xFFE50914).withOpacity(0.3)) : null,
                          ),
                          child: ListTile(
                            leading: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: _index == i ? const Color(0xFFE50914) : Colors.white12,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(_items[i].icon, color: Colors.white, size: 20),
                            ),
                            title: Text(_items[i].title, style: const TextStyle(color: Colors.white)),
                            onTap: () {
                              setState(() => _index = i);
                              Navigator.of(context).pop();
                            },
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _NavItem {
  final String title;
  final IconData icon;
  final Widget screen;
  const _NavItem(this.title, this.icon, this.screen);
}

class _HomeScreen extends StatelessWidget {
  const _HomeScreen();

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.black, Color(0xFF0A0A0A)],
        ),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFF1877F2), // Facebook blue
                    borderRadius: BorderRadius.circular(8), // Facebook radius
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF1877F2).withOpacity(0.2),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Icon(Icons.build_rounded, color: Colors.white, size: 28),
                ),
                const SizedBox(width: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Mobile Repair Hub',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).brightness == Brightness.dark
                          ? const Color(0xFFE4E6EA)
                          : const Color(0xFF1C1E21)
                      )),
                    Text('Professional Service Center',
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).brightness == Brightness.dark
                          ? const Color(0xFFB0B3B8)
                          : const Color(0xFF65676B)
                      )),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 24),
            const _QuickGrid(),
            const SizedBox(height: 24),
            const _StatGrid(),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}

class _QuickGrid extends StatelessWidget {
  const _QuickGrid();

  @override
  Widget build(BuildContext context) {
    return GridView(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: 16,
        crossAxisSpacing: 16,
        childAspectRatio: 1.4,
      ),
      children: const [
        _QuickButton(Color(0xFF1877F2), Icons.phone_android_rounded, 'New Repair', 'Register device repair', route: '/new'),
        _QuickButton(Color(0xFF42A5F5), Icons.person_add_alt_1_rounded, 'Add Customer', 'New client profile'),
        _QuickButton(Color(0xFFFF9500), Icons.inventory_2_rounded, 'Parts Stock', 'Manage spare parts'),
        _QuickButton(Color(0xFF42B883), Icons.analytics_rounded, 'Reports', 'Business analytics'),
      ],
    );
  }
}

class _QuickButton extends StatelessWidget {
  final Color color;
  final IconData icon;
  final String title;
  final String subtitle;
  final String? route;
  const _QuickButton(this.color, this.icon, this.title, this.subtitle, {this.route});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        if (title == 'Add Customer') {
          final created = await Navigator.of(context).push<Customer>(
            MaterialPageRoute(builder: (_) => const AddCustomerScreen(silentReturn: true)),
          );
          if (created != null) {
            Navigator.of(context).pushNamed('/new', arguments: created);
          }
          return;
        }
        if (title == 'Parts Stock') {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (_) => const InventoryScreen()),
          );
          return;
        }
        if (title == 'Reports') {
          // TODO: Navigate to reports screen
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Reports feature coming soon!')),
          );
          return;
        }
        if (route != null) {
          Navigator.of(context).pushNamed(route!);
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12), // Facebook card radius
          color: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF3A3B3C) // Facebook dark card
            : Colors.white, // Facebook light card
          border: Border.all(
            color: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFF3E4042) // Facebook dark border
              : const Color(0xFFDADDE1), // Facebook light border
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(icon, color: color, size: 20),
                  ),
                  const Spacer(),
                  Icon(Icons.arrow_forward_ios,
                    color: Theme.of(context).brightness == Brightness.dark
                      ? const Color(0xFFB0B3B8)
                      : const Color(0xFF65676B),
                    size: 12),
                ],
              ),
              const SizedBox(height: 12),
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        color: Theme.of(context).brightness == Brightness.dark
                          ? const Color(0xFFE4E6EA)
                          : const Color(0xFF1C1E21),
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        color: Theme.of(context).brightness == Brightness.dark
                          ? const Color(0xFFB0B3B8)
                          : const Color(0xFF65676B),
                        fontSize: 12,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _StatGrid extends StatelessWidget {
  const _StatGrid();

  @override
  Widget build(BuildContext context) {
    return GridView(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: 8,
        crossAxisSpacing: 8,
        childAspectRatio: 1.9,
      ),
      children: [
        Selector<AppState, int>(
          selector: (_, s) => s.orders
              .where((o) => !{
                    OrderStatus.delivered,
                    OrderStatus.cancelled,
                  }.contains(o.status))
              .length,
          builder: (_, value, __) => _StatCard('Active Repairs', '$value', Icons.build_rounded, Colors.blue),
        ),
        Selector<AppState, int>(
          selector: (_, s) => s.orders.where((o) => o.status == OrderStatus.readyForDelivery).length,
          builder: (_, value, __) => _StatCard('Ready for Delivery', '$value', Icons.local_shipping_rounded, Colors.green),
        ),
        Selector<AppState, int>(
          selector: (_, s) => s.inventory.where((i) => i.quantity <= i.lowStockThreshold).length,
          builder: (_, value, __) => _StatCard('Low Stock', '$value', Icons.warning_amber_rounded, Colors.red),
        ),
        Selector<AppState, int>(
          selector: (_, s) => s.wholesaleInventory.length,
          builder: (_, value, __) => _StatCard('Wholesale Lines', '$value', Icons.shopping_bag_rounded, Colors.orange),
        ),
        const _StatCard("Today's Earnings", '₹0', Icons.currency_rupee_rounded, Colors.purple),
      ],
    );
  }
}

class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  const _StatCard(this.title, this.value, this.icon, this.color);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(16),
      ),
      padding: const EdgeInsets.all(16),
      child: Stack(
        children: [
          Positioned(
            right: -8,
            bottom: -8,
            child: Icon(icon, size: 96, color: Colors.white.withValues(alpha: 0.08)),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(title, style: TextStyle(color: Colors.white.withValues(alpha: 0.9), fontSize: 12, fontWeight: FontWeight.w600)),
              Text(value, style: const TextStyle(color: Colors.white, fontSize: 26, fontWeight: FontWeight.bold)),
            ],
          ),
        ],
      ),
    );
  }
}

class _Placeholder extends StatelessWidget {
  final String title;
  const _Placeholder(this.title);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Text('$title screen coming soon...', style: const TextStyle(fontSize: 16)),
    );
  }
}
