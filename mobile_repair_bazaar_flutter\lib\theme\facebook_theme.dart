import 'package:flutter/material.dart';

/// Facebook-Style Mobile Repair Shop Theme Configuration
/// Modern Facebook UI design system for mobile device repair business management
class FacebookTheme {
  // Facebook Brand Colors
  static const Color facebookBlue = Color(0xFF1877F2);
  static const Color facebookDarkBlue = Color(0xFF166FE5);
  static const Color facebookLightBlue = Color(0xFF42A5F5);
  static const Color successGreen = Color(0xFF42B883);
  static const Color warningOrange = Color(0xFFFF9500);
  static const Color errorRed = Color(0xFFE74C3C);
  static const Color grayAccent = Color(0xFF8A8D91);
  
  // Facebook Dark Theme Colors
  static const Color darkBackground = Color(0xFF18191A);
  static const Color darkSurface = Color(0xFF242526);
  static const Color darkCard = Color(0xFF3A3B3C);
  static const Color darkBorder = Color(0xFF3E4042);
  static const Color darkText = Color(0xFFE4E6EA);
  static const Color darkSecondaryText = Color(0xFFB0B3B8);
  
  // Facebook Light Theme Colors
  static const Color lightBackground = Color(0xFFF0F2F5);
  static const Color lightSurface = Colors.white;
  static const Color lightCard = Colors.white;
  static const Color lightBorder = Color(0xFFDADDE1);
  static const Color lightText = Color(0xFF1C1E21);
  static const Color lightSecondaryText = Color(0xFF65676B);

  // Facebook Shadows
  static List<BoxShadow> facebookShadow = [
    BoxShadow(
      color: Colors.black.withOpacity(0.1),
      blurRadius: 2,
      offset: const Offset(0, 1),
    ),
  ];

  static List<BoxShadow> facebookCardShadow = [
    BoxShadow(
      color: Colors.black.withOpacity(0.08),
      blurRadius: 8,
      offset: const Offset(0, 2),
    ),
  ];

  // Facebook Border Radius
  static const BorderRadius facebookRadius = BorderRadius.all(Radius.circular(8));
  static const BorderRadius facebookCardRadius = BorderRadius.all(Radius.circular(12));
  static const BorderRadius facebookButtonRadius = BorderRadius.all(Radius.circular(6));

  // Facebook Typography
  static const TextStyle facebookHeading1 = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.bold,
    height: 1.2,
  );

  static const TextStyle facebookHeading2 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    height: 1.3,
  );

  static const TextStyle facebookHeading3 = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    height: 1.3,
  );

  static const TextStyle facebookBody1 = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    height: 1.4,
  );

  static const TextStyle facebookBody2 = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    height: 1.4,
  );

  static const TextStyle facebookCaption = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    height: 1.3,
  );

  static const TextStyle facebookButton = TextStyle(
    fontSize: 15,
    fontWeight: FontWeight.w600,
    height: 1.2,
  );

  // Status Colors for Repair Orders (Facebook-style)
  static const Map<String, Color> statusColors = {
    'pending': warningOrange,
    'in_progress': facebookBlue,
    'ready': successGreen,
    'delivered': successGreen,
    'cancelled': errorRed,
  };

  // Device Type Colors (Facebook-style)
  static const Map<String, Color> deviceColors = {
    'smartphone': facebookBlue,
    'tablet': facebookLightBlue,
    'laptop': grayAccent,
    'smartwatch': warningOrange,
    'other': grayAccent,
  };

  // Priority Colors (Facebook-style)
  static const Map<String, Color> priorityColors = {
    'low': successGreen,
    'medium': warningOrange,
    'high': errorRed,
    'urgent': Color(0xFFD32F2F),
  };
}

/// Facebook-Style Custom Widgets for Mobile Repair Shop UI
class FacebookWidgets {
  
  /// Facebook-style Status Badge
  static Widget statusBadge(String status, {double? fontSize}) {
    final color = FacebookTheme.statusColors[status.toLowerCase()] ?? FacebookTheme.grayAccent;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: FacebookTheme.facebookRadius,
        border: Border.all(color: color.withOpacity(0.3), width: 1),
      ),
      child: Text(
        status.toUpperCase(),
        style: FacebookTheme.facebookCaption.copyWith(
          color: color,
          fontSize: fontSize ?? 11,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// Facebook-style Device Icon
  static Widget deviceIcon(String deviceType, {double size = 24}) {
    IconData icon;
    Color color = FacebookTheme.deviceColors[deviceType.toLowerCase()] ?? FacebookTheme.grayAccent;
    
    switch (deviceType.toLowerCase()) {
      case 'smartphone':
        icon = Icons.smartphone;
        break;
      case 'tablet':
        icon = Icons.tablet;
        break;
      case 'laptop':
        icon = Icons.laptop;
        break;
      case 'smartwatch':
        icon = Icons.watch;
        break;
      default:
        icon = Icons.devices;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: FacebookTheme.facebookRadius,
        border: Border.all(color: color.withOpacity(0.2), width: 1),
      ),
      child: Icon(icon, color: color, size: size),
    );
  }

  /// Facebook-style Priority Indicator
  static Widget priorityIndicator(String priority, {double size = 12}) {
    final color = FacebookTheme.priorityColors[priority.toLowerCase()] ?? FacebookTheme.grayAccent;
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    );
  }

  /// Facebook-style Card Container
  static Widget facebookCard({
    required Widget child,
    EdgeInsets? padding,
    EdgeInsets? margin,
    Color? color,
    bool isDark = false,
  }) {
    return Container(
      margin: margin ?? const EdgeInsets.symmetric(vertical: 4),
      padding: padding ?? const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color ?? (isDark ? FacebookTheme.darkCard : FacebookTheme.lightCard),
        borderRadius: FacebookTheme.facebookCardRadius,
        boxShadow: FacebookTheme.facebookCardShadow,
        border: Border.all(
          color: isDark ? FacebookTheme.darkBorder : FacebookTheme.lightBorder,
          width: 1,
        ),
      ),
      child: child,
    );
  }

  /// Facebook-style Button
  static Widget facebookButton({
    required String text,
    required VoidCallback onPressed,
    bool isPrimary = true,
    bool isOutlined = false,
    EdgeInsets? padding,
    double? width,
    bool isDark = false,
  }) {
    Color backgroundColor;
    Color textColor;
    Color borderColor;

    if (isPrimary && !isOutlined) {
      backgroundColor = FacebookTheme.facebookBlue;
      textColor = Colors.white;
      borderColor = FacebookTheme.facebookBlue;
    } else if (isOutlined) {
      backgroundColor = Colors.transparent;
      textColor = isDark ? FacebookTheme.darkText : FacebookTheme.facebookBlue;
      borderColor = FacebookTheme.facebookBlue;
    } else {
      backgroundColor = isDark ? FacebookTheme.darkSurface : const Color(0xFFF0F2F5);
      textColor = isDark ? FacebookTheme.darkText : FacebookTheme.lightText;
      borderColor = isDark ? FacebookTheme.darkBorder : FacebookTheme.lightBorder;
    }

    return Container(
      width: width,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: FacebookTheme.facebookButtonRadius,
        border: Border.all(color: borderColor, width: 1),
        boxShadow: isPrimary && !isOutlined ? FacebookTheme.facebookShadow : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: FacebookTheme.facebookButtonRadius,
          child: Padding(
            padding: padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Center(
              child: Text(
                text,
                style: FacebookTheme.facebookButton.copyWith(color: textColor),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Facebook-style Input Field
  static Widget facebookInput({
    required String label,
    String? hint,
    TextEditingController? controller,
    bool isDark = false,
    IconData? prefixIcon,
    Widget? suffixIcon,
    Function(String)? onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FacebookTheme.facebookBody2.copyWith(
            color: isDark ? FacebookTheme.darkSecondaryText : FacebookTheme.lightSecondaryText,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 6),
        Container(
          decoration: BoxDecoration(
            color: isDark ? FacebookTheme.darkSurface : FacebookTheme.lightSurface,
            borderRadius: FacebookTheme.facebookRadius,
            border: Border.all(
              color: isDark ? FacebookTheme.darkBorder : FacebookTheme.lightBorder,
              width: 1,
            ),
          ),
          child: TextField(
            controller: controller,
            onChanged: onChanged,
            style: FacebookTheme.facebookBody1.copyWith(
              color: isDark ? FacebookTheme.darkText : FacebookTheme.lightText,
            ),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: FacebookTheme.facebookBody1.copyWith(
                color: isDark ? FacebookTheme.darkSecondaryText : FacebookTheme.lightSecondaryText,
              ),
              prefixIcon: prefixIcon != null 
                ? Icon(prefixIcon, color: FacebookTheme.grayAccent, size: 20)
                : null,
              suffixIcon: suffixIcon,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
            ),
          ),
        ),
      ],
    );
  }
}
