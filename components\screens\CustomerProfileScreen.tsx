

import React, { useState, useEffect, useRef } from 'react';
import { useApp } from '../../context/AppContext';
import Card from '../ui/Card';
import Button from '../ui/Button';
import StatusBadge from '../ui/StatusBadge';
import TextArea from '../ui/TextArea';
import ActionSheet from '../ui/ActionSheet';
import CameraModal from '../ui/CameraModal';
import { User, Phone, MapPin, MessageCircle, DollarSign, Wrench, Calendar, Save, Trash2, UploadCloud, Camera as CameraIcon, ImageIcon } from 'lucide-react';

const StatCard: React.FC<{ title: string; value: string | number; icon: React.ReactNode }> = ({ title, value, icon }) => (
    <Card className="flex items-center gap-4">
        <div className="p-3 bg-accent-light text-accent rounded-lg">
            {icon}
        </div>
        <div>
            <p className="text-sm text-gray-500">{title}</p>
            <p className="font-bold text-lg text-gray-800">{value}</p>
        </div>
    </Card>
);

const resizeImage = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (event) => {
            const img = new Image();
            img.src = event.target?.result as string;
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const MAX_WIDTH = 256;
                const MAX_HEIGHT = 256;
                let { width, height } = img;

                if (width > height) {
                    if (width > MAX_WIDTH) {
                        height *= MAX_WIDTH / width;
                        width = MAX_WIDTH;
                    }
                } else {
                    if (height > MAX_HEIGHT) {
                        width *= MAX_HEIGHT / height;
                        height = MAX_HEIGHT;
                    }
                }
                canvas.width = width;
                canvas.height = height;
                const ctx = canvas.getContext('2d');
                if (!ctx) return reject(new Error('Could not get canvas context'));
                ctx.drawImage(img, 0, 0, width, height);
                resolve(canvas.toDataURL('image/jpeg', 0.9));
            };
            img.onerror = (error) => reject(error);
        };
        reader.onerror = (error) => reject(error);
    });
};


const CustomerProfileScreen: React.FC = () => {
    const { 
        customers, 
        activeCustomerId, 
        orders, 
        viewOrderDetails, 
        updateCustomer,
        deleteCustomer,
        setActiveScreen,
        setToast
    } = useApp();
    
    const customer = customers.find(c => c.id === activeCustomerId);
    const [notes, setNotes] = useState(customer?.notes || '');
    const [isActionSheetOpen, setActionSheetOpen] = useState(false);
    const [isCameraOpen, setCameraOpen] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        setNotes(customer?.notes || '');
    }, [customer]);
    
    if (!customer) {
        return <Card className="text-center p-8">Customer not found.</Card>;
    }

    const customerOrders = orders.filter(o => o.customer.id === customer.id).sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    
    const totalSpent = customerOrders.reduce((sum, order) => sum + order.estimatedCost, 0);
    const lastVisit = customerOrders.length > 0 ? new Date(customerOrders[0].createdAt).toLocaleDateString() : 'N/A';

    const handleSaveNotes = () => {
        updateCustomer({ ...customer, notes });
    };

    const handleDelete = () => {
        deleteCustomer(customer.id);
        setActiveScreen('Customers');
    };

    const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            try {
                const resizedImage = await resizeImage(file);
                updateCustomer({ ...customer, avatarUrl: resizedImage });
                setToast("Profile photo updated!");
            } catch (error) {
                console.error("Error resizing image:", error);
                setToast("Could not process image.");
            }
        }
        event.target.value = '';
    };
    
    const handleCapture = (base64Image: string) => {
        updateCustomer({ ...customer, avatarUrl: base64Image });
        setToast("Profile photo updated!");
        setCameraOpen(false);
    };

    return (
        <div className="space-y-6">
            <input type="file" accept="image/*" ref={fileInputRef} onChange={handleFileChange} className="hidden" />
            <ActionSheet isOpen={isActionSheetOpen} onClose={() => setActionSheetOpen(false)} title="Change Profile Photo">
                <Button variant="ghost" className="w-full !justify-start" onClick={() => {setActionSheetOpen(false); setCameraOpen(true)}}><CameraIcon size={20}/>Take Photo</Button>
                <Button variant="ghost" className="w-full !justify-start" onClick={() => {setActionSheetOpen(false); fileInputRef.current?.click()}}><ImageIcon size={20}/>Choose from Gallery</Button>
            </ActionSheet>
            <CameraModal isOpen={isCameraOpen} onClose={() => setCameraOpen(false)} onCapture={handleCapture} />

            {/* Customer Header */}
            <Card className="!p-6">
                <div className="flex flex-col items-center text-center">
                    <div className="relative w-24 h-24 mb-4 rounded-full bg-accent-light flex items-center justify-center group">
                        {customer.avatarUrl ? (
                            <img src={customer.avatarUrl} alt={customer.name} className="w-full h-full rounded-full object-cover" />
                        ) : (
                            <User size={48} className="text-accent" />
                        )}
                         <button 
                            onClick={() => setActionSheetOpen(true)}
                            className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-opacity"
                            aria-label="Change profile photo"
                        >
                            <UploadCloud size={24} />
                        </button>
                    </div>
                    <h2 className="text-2xl font-bold text-gray-800">{customer.name}</h2>
                    <p className="text-gray-500 flex items-center gap-2 mt-1"><Phone size={14} />{customer.phone}</p>
                    {customer.address && <p className="text-gray-500 flex items-center gap-2 mt-1 text-center"><MapPin size={14} />{customer.address}</p>}
                </div>
                <div className="mt-6 flex gap-2">
                    <Button variant="primary" className="flex-1" onClick={() => window.location.href = `tel:${customer.phone}`}>
                        <Phone size={16} /> Call
                    </Button>
                    <Button variant="secondary" className="flex-1 !bg-green-100 !text-green-700" onClick={() => window.open(`https://wa.me/${customer.phone.replace(/\D/g, '')}`, '_blank')}>
                        <MessageCircle size={16} /> WhatsApp
                    </Button>
                </div>
            </Card>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <StatCard title="Total Repairs" value={customerOrders.length} icon={<Wrench size={24} />} />
                <StatCard title="Total Spent" value={`₹${totalSpent.toLocaleString()}`} icon={<DollarSign size={24} />} />
                <StatCard title="Last Visit" value={lastVisit} icon={<Calendar size={24} />} />
            </div>

            {/* Repair History */}
            <Card>
                <h3 className="text-lg font-bold text-gray-700 mb-2">Repair History</h3>
                <div className="space-y-3">
                    {customerOrders.length > 0 ? (
                        customerOrders.map(order => (
                            <button 
                                key={order.id} 
                                onClick={() => viewOrderDetails(order.id)}
                                className="w-full text-left p-3 flex items-center gap-4 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
                            >
                                <div className="flex-grow">
                                    <p className="font-semibold text-gray-800">{order.deviceModel}</p>
                                    <p className="text-xs text-gray-500">{new Date(order.createdAt).toLocaleDateString()}</p>
                                </div>
                                <StatusBadge status={order.status} />
                            </button>
                        ))
                    ) : (
                        <p className="text-center text-gray-500 py-4">No repair history found.</p>
                    )}
                </div>
            </Card>

            {/* Notes */}
            <Card>
                 <h3 className="text-lg font-bold text-gray-700 mb-2">Notes & Communication Log</h3>
                 <TextArea
                    label=""
                    name="notes"
                    rows={4}
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="Add notes about conversations, special requests, etc."
                 />
                 <div className="mt-4 flex justify-end">
                     <Button onClick={handleSaveNotes}>
                         <Save size={16} />
                         Save Notes
                     </Button>
                 </div>
            </Card>
            
            {/* Danger Zone */}
            <Card className="border-red-500/20 bg-red-50/50">
                 <h3 className="text-lg font-bold text-red-700 mb-2">Danger Zone</h3>
                 <div className="flex justify-between items-center">
                    <p className="text-sm text-red-600">Permanently delete this customer. This action cannot be undone.</p>
                    <Button variant="secondary" className="!bg-red-100 !text-red-700 hover:!bg-red-200" onClick={handleDelete}>
                        <Trash2 size={16} />
                        Delete
                    </Button>
                 </div>
            </Card>

        </div>
    );
};

export default CustomerProfileScreen;