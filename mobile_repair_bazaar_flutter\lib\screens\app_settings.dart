import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../app_state.dart';

class AppSettingsScreen extends StatefulWidget {
  const AppSettingsScreen({super.key});

  @override
  State<AppSettingsScreen> createState() => _AppSettingsScreenState();
}

class _AppSettingsScreenState extends State<AppSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: isDark
            ? [Colors.black, const Color(0xFF0A0A0A)]
            : [Colors.grey[50]!, Colors.white],
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          title: Text(
            'App Settings',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onBackground,
            ),
          ),
        ),
        body: Consumer<AppState>(
          builder: (context, appState, _) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Appearance Settings
                  _buildSectionTitle('Appearance'),
                  const SizedBox(height: 16),
                  _buildSettingsCard([
                    _buildSwitchTile(
                      'Dark Mode',
                      'Use dark theme throughout the app',
                      Icons.dark_mode,
                      appState.appSettings.isDarkMode,
                      (value) => appState.updateSetting('isDarkMode', value),
                    ),
                  ]),

                  const SizedBox(height: 24),

                  // Language & Region
                  _buildSectionTitle('Language & Region'),
                  const SizedBox(height: 16),
                  _buildSettingsCard([
                    _buildDropdownTile(
                      'Language',
                      'Choose app language',
                      Icons.language,
                      appState.appSettings.language,
                      ['English', 'Hindi', 'Tamil', 'Telugu', 'Bengali'],
                      (value) => appState.updateSetting('language', value),
                    ),
                    _buildDropdownTile(
                      'Currency',
                      'Select default currency',
                      Icons.currency_rupee,
                      appState.appSettings.currency,
                      ['₹ INR', '\$ USD', '€ EUR', '£ GBP'],
                      (value) => appState.updateSetting('currency', value),
                    ),
                    _buildDropdownTile(
                      'Date Format',
                      'Choose date display format',
                      Icons.calendar_today,
                      appState.appSettings.dateFormat,
                      ['DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD'],
                      (value) => appState.updateSetting('dateFormat', value),
                    ),
                    _buildDropdownTile(
                      'Time Format',
                      'Choose time display format',
                      Icons.access_time,
                      appState.appSettings.timeFormat,
                      ['24h', '12h'],
                      (value) => appState.updateSetting('timeFormat', value),
                    ),
                  ]),

                  const SizedBox(height: 24),

                  // Notifications
                  _buildSectionTitle('Notifications'),
                  const SizedBox(height: 16),
                  _buildSettingsCard([
                    _buildSwitchTile(
                      'Push Notifications',
                      'Receive app notifications',
                      Icons.notifications,
                      appState.appSettings.notificationsEnabled,
                      (value) => appState.updateSetting('notificationsEnabled', value),
                    ),
                  ]),

                  const SizedBox(height: 24),

                  // Data & Storage
                  _buildSectionTitle('Data & Storage'),
                  const SizedBox(height: 16),
                  _buildSettingsCard([
                    _buildSwitchTile(
                      'Auto Backup',
                      'Automatically backup data daily',
                      Icons.backup,
                      appState.appSettings.autoBackup,
                      (value) => appState.updateSetting('autoBackup', value),
                    ),
                  ]),

                  const SizedBox(height: 24),

                  // Security
                  _buildSectionTitle('Security'),
                  const SizedBox(height: 16),
                  _buildSettingsCard([
                    _buildSwitchTile(
                      'Biometric Lock',
                      'Use fingerprint/face unlock',
                      Icons.fingerprint,
                      appState.appSettings.biometricEnabled,
                      (value) => appState.updateSetting('biometricEnabled', value),
                    ),
                    _buildDropdownTile(
                      'Auto Logout',
                      'Automatically logout after inactivity',
                      Icons.logout,
                      '${appState.appSettings.autoLogoutMinutes} minutes',
                      ['15 minutes', '30 minutes', '60 minutes', 'Never'],
                      (value) {
                        int minutes;
                        switch (value) {
                          case '15 minutes':
                            minutes = 15;
                            break;
                          case '30 minutes':
                            minutes = 30;
                            break;
                          case '60 minutes':
                            minutes = 60;
                            break;
                          case 'Never':
                            minutes = 0;
                            break;
                          default:
                            minutes = 30;
                        }
                        appState.updateSetting('autoLogoutMinutes', minutes);
                      },
                    ),
                  ]),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        color: Theme.of(context).colorScheme.onBackground,
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildSettingsCard(List<Widget> children) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDark ? Colors.white12 : Colors.grey.withOpacity(0.3),
        ),
      ),
      child: Column(
        children: children.asMap().entries.map((entry) {
          final index = entry.key;
          final child = entry.value;
          final isLast = index == children.length - 1;

          return Column(
            children: [
              child,
              if (!isLast)
              Divider(
                color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white12
                  : Colors.grey.withOpacity(0.3),
                height: 1,
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    IconData icon,
    bool value,
    Function(bool) onChanged,
  ) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: const Color(0xFFE50914).withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: const Color(0xFFE50914), size: 20),
      ),
      title: Text(
        title,
        style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          fontSize: 12,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: const Color(0xFFE50914),
      ),
    );
  }

  Widget _buildDropdownTile(
    String title,
    String subtitle,
    IconData icon,
    String currentValue,
    List<String> options,
    Function(String) onChanged,
  ) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: const Color(0xFFE50914).withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: const Color(0xFFE50914), size: 20),
      ),
      title: Text(
        title,
        style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            subtitle,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF2A2A2A)
                : Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white24
                  : Colors.grey.withOpacity(0.5),
              ),
            ),
            child: DropdownButton<String>(
              value: currentValue,
              dropdownColor: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF2A2A2A)
                : Colors.grey[100],
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface,
                fontSize: 14,
              ),
              underline: const SizedBox(),
              isExpanded: true,
              items: options.map((option) {
                return DropdownMenuItem(
                  value: option,
                  child: Text(option),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) onChanged(value);
              },
            ),
          ),
        ],
      ),
    );
  }
}
