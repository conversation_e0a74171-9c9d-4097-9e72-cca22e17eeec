import 'package:url_launcher/url_launcher.dart';

Future<void> launchTel(String phone) async {
  final uri = Uri(scheme: 'tel', path: phone);
  if (!await launchUrl(uri)) {
    // ignore
  }
}

Future<void> launchWhatsAppText(String phone, String text) async {
  final uri = Uri.parse('https://wa.me/${phone.replaceAll(RegExp(r'\D'), '')}?text=${Uri.encodeComponent(text)}');
  if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
    // ignore
  }
}

Future<void> openUrl(Uri uri) async {
  await launchUrl(uri, mode: LaunchMode.externalApplication);
}


