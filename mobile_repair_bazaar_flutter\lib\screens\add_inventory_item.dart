import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import '../app_state.dart';
import '../models.dart';

class AddInventoryItemScreen extends StatefulWidget {
  final InventoryItem? existingItem;
  
  const AddInventoryItemScreen({super.key, this.existingItem});

  @override
  State<AddInventoryItemScreen> createState() => _AddInventoryItemScreenState();
}

class _AddInventoryItemScreenState extends State<AddInventoryItemScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _brandController = TextEditingController();
  final _modelController = TextEditingController();
  final _quantityController = TextEditingController();
  final _costPriceController = TextEditingController();
  final _sellingPriceController = TextEditingController();
  final _lowStockThresholdController = TextEditingController();
  final _supplierController = TextEditingController();
  final _locationController = TextEditingController();
  final _barcodeController = TextEditingController();

  InventoryCategory _selectedCategory = InventoryCategory.other;
  File? _selectedImage;
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    if (widget.existingItem != null) {
      _populateFields(widget.existingItem!);
    }
  }

  void _populateFields(InventoryItem item) {
    _nameController.text = item.name;
    _descriptionController.text = item.description ?? '';
    _brandController.text = item.brand ?? '';
    _modelController.text = item.model ?? '';
    _quantityController.text = item.quantity.toString();
    _costPriceController.text = item.costPrice.toString();
    _sellingPriceController.text = item.sellingPrice.toString();
    _lowStockThresholdController.text = item.lowStockThreshold.toString();
    _supplierController.text = item.supplier ?? '';
    _locationController.text = item.location ?? '';
    _barcodeController.text = item.barcode ?? '';
    _selectedCategory = item.category;
    if (item.imageUrl != null) {
      _selectedImage = File(item.imageUrl!);
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _brandController.dispose();
    _modelController.dispose();
    _quantityController.dispose();
    _costPriceController.dispose();
    _sellingPriceController.dispose();
    _lowStockThresholdController.dispose();
    _supplierController.dispose();
    _locationController.dispose();
    _barcodeController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        _selectedImage = File(image.path);
      });
    }
  }

  Future<void> _saveItem() async {
    if (!_formKey.currentState!.validate()) return;

    final appState = context.read<AppState>();
    final now = DateTime.now();

    final item = InventoryItem(
      id: widget.existingItem?.id ?? 'INV-${now.millisecondsSinceEpoch}',
      name: _nameController.text.trim(),
      description: _descriptionController.text.trim().isEmpty ? null : _descriptionController.text.trim(),
      category: _selectedCategory,
      brand: _brandController.text.trim().isEmpty ? null : _brandController.text.trim(),
      model: _modelController.text.trim().isEmpty ? null : _modelController.text.trim(),
      quantity: int.parse(_quantityController.text),
      costPrice: double.parse(_costPriceController.text),
      sellingPrice: double.parse(_sellingPriceController.text),
      lowStockThreshold: int.parse(_lowStockThresholdController.text),
      supplier: _supplierController.text.trim().isEmpty ? null : _supplierController.text.trim(),
      location: _locationController.text.trim().isEmpty ? null : _locationController.text.trim(),
      createdAt: widget.existingItem?.createdAt ?? now,
      updatedAt: now,
      imageUrl: _selectedImage?.path,
      barcode: _barcodeController.text.trim().isEmpty ? null : _barcodeController.text.trim(),
    );

    if (widget.existingItem != null) {
      await appState.updateInventoryItem(item);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Inventory item updated successfully'),
            backgroundColor: Color(0xFFE50914),
          ),
        );
      }
    } else {
      await appState.addInventoryItem(item);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Inventory item added successfully'),
            backgroundColor: Color(0xFFE50914),
          ),
        );
      }
    }

    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.black, Color(0xFF0A0A0A)],
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          title: Text(
            widget.existingItem != null ? 'Edit Inventory Item' : 'Add Inventory Item',
            style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
          ),
          actions: [
            if (widget.existingItem != null)
              IconButton(
                icon: const Icon(Icons.delete, color: Colors.red),
                onPressed: () => _showDeleteDialog(),
              ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Image Section
                Center(
                  child: GestureDetector(
                    onTap: _pickImage,
                    child: Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: const Color(0xFF1F1F1F),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: Colors.white24),
                      ),
                      child: _selectedImage != null
                          ? ClipRRect(
                              borderRadius: BorderRadius.circular(16),
                              child: Image.file(
                                _selectedImage!,
                                fit: BoxFit.cover,
                              ),
                            )
                          : const Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.add_a_photo, color: Colors.white54, size: 32),
                                SizedBox(height: 8),
                                Text('Add Photo', style: TextStyle(color: Colors.white54)),
                              ],
                            ),
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Basic Information
                _buildSectionTitle('Basic Information'),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _nameController,
                  label: 'Item Name',
                  icon: Icons.inventory_2,
                  validator: (value) => value?.isEmpty == true ? 'Please enter item name' : null,
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _descriptionController,
                  label: 'Description (Optional)',
                  icon: Icons.description,
                  maxLines: 3,
                ),
                const SizedBox(height: 16),

                // Category Selection
                _buildSectionTitle('Category'),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: const Color(0xFF1F1F1F),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.white24),
                  ),
                  child: DropdownButton<InventoryCategory>(
                    value: _selectedCategory,
                    isExpanded: true,
                    dropdownColor: const Color(0xFF1F1F1F),
                    style: const TextStyle(color: Colors.white),
                    underline: const SizedBox(),
                    items: InventoryCategory.values.map((category) {
                      return DropdownMenuItem(
                        value: category,
                        child: Text(_getCategoryDisplayName(category)),
                      );
                    }).toList(),
                    onChanged: (value) => setState(() => _selectedCategory = value!),
                  ),
                ),
                const SizedBox(height: 24),

                // Device Information
                _buildSectionTitle('Device Information'),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildTextField(
                        controller: _brandController,
                        label: 'Brand (Optional)',
                        icon: Icons.business,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildTextField(
                        controller: _modelController,
                        label: 'Model (Optional)',
                        icon: Icons.phone_android,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Stock & Pricing
                _buildSectionTitle('Stock & Pricing'),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildTextField(
                        controller: _quantityController,
                        label: 'Quantity',
                        icon: Icons.numbers,
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value?.isEmpty == true) return 'Required';
                          if (int.tryParse(value!) == null) return 'Invalid number';
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildTextField(
                        controller: _lowStockThresholdController,
                        label: 'Low Stock Alert',
                        icon: Icons.warning,
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value?.isEmpty == true) return 'Required';
                          if (int.tryParse(value!) == null) return 'Invalid number';
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildTextField(
                        controller: _costPriceController,
                        label: 'Cost Price (₹)',
                        icon: Icons.money,
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value?.isEmpty == true) return 'Required';
                          if (double.tryParse(value!) == null) return 'Invalid amount';
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildTextField(
                        controller: _sellingPriceController,
                        label: 'Selling Price (₹)',
                        icon: Icons.sell,
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value?.isEmpty == true) return 'Required';
                          if (double.tryParse(value!) == null) return 'Invalid amount';
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Additional Information
                _buildSectionTitle('Additional Information'),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _supplierController,
                  label: 'Supplier (Optional)',
                  icon: Icons.store,
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _locationController,
                  label: 'Storage Location (Optional)',
                  icon: Icons.location_on,
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _barcodeController,
                  label: 'Barcode/SKU (Optional)',
                  icon: Icons.qr_code,
                ),
                const SizedBox(height: 32),

                // Save Button
                SizedBox(
                  width: double.infinity,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFFE50914), Color(0xFFB20710)],
                      ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: ElevatedButton(
                      onPressed: _saveItem,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        shadowColor: Colors.transparent,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      child: Text(
                        widget.existingItem != null ? 'Update Item' : 'Add Item',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getCategoryDisplayName(InventoryCategory category) {
    switch (category) {
      case InventoryCategory.screen:
        return 'Screen';
      case InventoryCategory.battery:
        return 'Battery';
      case InventoryCategory.charger:
        return 'Charger';
      case InventoryCategory.speaker:
        return 'Speaker';
      case InventoryCategory.camera:
        return 'Camera';
      case InventoryCategory.motherboard:
        return 'Motherboard';
      case InventoryCategory.backCover:
        return 'Back Cover';
      case InventoryCategory.flex:
        return 'Flex';
      case InventoryCategory.sensor:
        return 'Sensor';
      case InventoryCategory.other:
        return 'Other';
    }
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        color: Colors.white,
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: validator,
      maxLines: maxLines,
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: const TextStyle(color: Colors.white70),
        prefixIcon: Icon(icon, color: Colors.white70),
        filled: true,
        fillColor: const Color(0xFF1F1F1F),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.white24),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.white24),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFFE50914)),
        ),
      ),
    );
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1F1F1F),
        title: const Text('Delete Item', style: TextStyle(color: Colors.white)),
        content: const Text(
          'Are you sure you want to delete this inventory item? This action cannot be undone.',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel', style: TextStyle(color: Colors.white70)),
          ),
          Container(
            decoration: BoxDecoration(
              gradient: const LinearGradient(colors: [Colors.red, Color(0xFFB71C1C)]),
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                final appState = context.read<AppState>();
                await appState.deleteInventoryItem(widget.existingItem!.id);
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Inventory item deleted successfully'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  Navigator.of(context).pop();
                }
              },
              style: TextButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: Colors.white,
              ),
              child: const Text('Delete'),
            ),
          ),
        ],
      ),
    );
  }
}
