// Core domain models mirroring types.ts from the React app

enum OrderStatus {
  received,
  diagnosing,
  repairInProgress,
  qualityCheck,
  readyForDelivery,
  delivered,
  cancelled,
}

enum TechnicianRole { owner, technician, cashier }

enum JobPriority { normal, urgent, vip }

class BusinessHour {
  final String day; // Monday..Sunday
  final bool isOpen;
  final String openTime; // HH:mm
  final String closeTime; // HH:mm

  const BusinessHour({
    required this.day,
    required this.isOpen,
    required this.openTime,
    required this.closeTime,
  });

  Map<String, dynamic> toJson() => {
        'day': day,
        'isOpen': isOpen,
        'openTime': openTime,
        'closeTime': closeTime,
      };

  factory BusinessHour.fromJson(Map<String, dynamic> json) => BusinessHour(
        day: json['day'] as String,
        isOpen: json['isOpen'] as bool,
        openTime: json['openTime'] as String,
        closeTime: json['closeTime'] as String,
      );
}

class ShopProfile {
  final String name;
  final String phone;
  final String address;
  final String? avatarUrl;
  final String? bannerUrl;

  const ShopProfile({
    required this.name,
    this.phone = '',
    this.address = '',
    this.avatarUrl,
    this.bannerUrl,
  });

  ShopProfile copyWith({String? name, String? phone, String? address, String? avatarUrl, String? bannerUrl}) =>
      ShopProfile(
        name: name ?? this.name,
        phone: phone ?? this.phone,
        address: address ?? this.address,
        avatarUrl: avatarUrl ?? this.avatarUrl,
        bannerUrl: bannerUrl ?? this.bannerUrl,
      );

  Map<String, dynamic> toJson() => {
        'name': name,
        'phone': phone,
        'address': address,
        'avatarUrl': avatarUrl,
        'bannerUrl': bannerUrl,
      };

  factory ShopProfile.fromJson(Map<String, dynamic> json) => ShopProfile(
        name: json['name'] as String,
        phone: (json['phone'] ?? '') as String,
        address: (json['address'] ?? '') as String,
        avatarUrl: json['avatarUrl'] as String?,
        bannerUrl: json['bannerUrl'] as String?,
      );
}

class Technician {
  final String id;
  final String name;
  final TechnicianRole role;
  final String? avatarUrl;
  final String? phone;

  const Technician({
    required this.id,
    required this.name,
    required this.role,
    this.avatarUrl,
    this.phone,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'role': role.name,
        'avatarUrl': avatarUrl,
        'phone': phone,
      };

  factory Technician.fromJson(Map<String, dynamic> json) => Technician(
        id: json['id'] as String,
        name: json['name'] as String,
        role: TechnicianRole.values.firstWhere((e) => e.name == json['role']),
        avatarUrl: json['avatarUrl'] as String?,
        phone: json['phone'] as String?,
      );
}

class Customer {
  final String id;
  final String name;
  final String phone;
  final String? address;
  final String? notes;
  final String? avatarUrl;

  const Customer({
    required this.id,
    required this.name,
    required this.phone,
    this.address,
    this.notes,
    this.avatarUrl,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'phone': phone,
        'address': address,
        'notes': notes,
        'avatarUrl': avatarUrl,
      };

  factory Customer.fromJson(Map<String, dynamic> json) => Customer(
        id: json['id'] as String,
        name: json['name'] as String,
        phone: json['phone'] as String,
        address: json['address'] as String?,
        notes: json['notes'] as String?,
        avatarUrl: json['avatarUrl'] as String?,
      );
}

enum InventoryCategory {
  screen, battery, charger, speaker, camera, motherboard,
  backCover, flex, sensor, other
}

class InventoryItem {
  final String id;
  final String name;
  final String? description;
  final InventoryCategory category;
  final String? brand;
  final String? model;
  final int quantity;
  final double costPrice;
  final double sellingPrice;
  final int lowStockThreshold;
  final String? supplier;
  final String? location;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? imageUrl;
  final String? barcode;

  const InventoryItem({
    required this.id,
    required this.name,
    this.description,
    required this.category,
    this.brand,
    this.model,
    required this.quantity,
    required this.costPrice,
    required this.sellingPrice,
    required this.lowStockThreshold,
    this.supplier,
    this.location,
    required this.createdAt,
    required this.updatedAt,
    this.imageUrl,
    this.barcode,
  });

  bool get isLowStock => quantity <= lowStockThreshold;
  bool get isOutOfStock => quantity <= 0;
  double get profitMargin => sellingPrice - costPrice;
  double get profitPercentage => costPrice > 0 ? (profitMargin / costPrice) * 100 : 0;

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'description': description,
        'category': category.name,
        'brand': brand,
        'model': model,
        'quantity': quantity,
        'costPrice': costPrice,
        'sellingPrice': sellingPrice,
        'lowStockThreshold': lowStockThreshold,
        'supplier': supplier,
        'location': location,
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'imageUrl': imageUrl,
        'barcode': barcode,
      };

  factory InventoryItem.fromJson(Map<String, dynamic> json) => InventoryItem(
        id: json['id'] as String,
        name: json['name'] as String,
        description: json['description'] as String?,
        category: InventoryCategory.values.firstWhere(
          (e) => e.name == json['category'],
          orElse: () => InventoryCategory.other,
        ),
        brand: json['brand'] as String?,
        model: json['model'] as String?,
        quantity: (json['quantity'] as num).toInt(),
        costPrice: (json['costPrice'] as num).toDouble(),
        sellingPrice: (json['sellingPrice'] as num).toDouble(),
        lowStockThreshold: (json['lowStockThreshold'] as num).toInt(),
        supplier: json['supplier'] as String?,
        location: json['location'] as String?,
        createdAt: DateTime.parse(json['createdAt'] as String? ?? DateTime.now().toIso8601String()),
        updatedAt: DateTime.parse(json['updatedAt'] as String? ?? DateTime.now().toIso8601String()),
        imageUrl: json['imageUrl'] as String?,
        barcode: json['barcode'] as String?,
      );

  InventoryItem copyWith({
    String? id,
    String? name,
    String? description,
    InventoryCategory? category,
    String? brand,
    String? model,
    int? quantity,
    double? costPrice,
    double? sellingPrice,
    int? lowStockThreshold,
    String? supplier,
    String? location,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? imageUrl,
    String? barcode,
  }) {
    return InventoryItem(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      brand: brand ?? this.brand,
      model: model ?? this.model,
      quantity: quantity ?? this.quantity,
      costPrice: costPrice ?? this.costPrice,
      sellingPrice: sellingPrice ?? this.sellingPrice,
      lowStockThreshold: lowStockThreshold ?? this.lowStockThreshold,
      supplier: supplier ?? this.supplier,
      location: location ?? this.location,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      imageUrl: imageUrl ?? this.imageUrl,
      barcode: barcode ?? this.barcode,
    );
  }
}

class WholesaleItem {
  final String id;
  final String name;
  final String category;
  final String supplier;
  final double costPrice;
  final double wholesalePrice;
  final int moq;
  final int stock;

  const WholesaleItem({
    required this.id,
    required this.name,
    required this.category,
    required this.supplier,
    required this.costPrice,
    required this.wholesalePrice,
    required this.moq,
    required this.stock,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'category': category,
        'supplier': supplier,
        'costPrice': costPrice,
        'wholesalePrice': wholesalePrice,
        'moq': moq,
        'stock': stock,
      };

  factory WholesaleItem.fromJson(Map<String, dynamic> json) => WholesaleItem(
        id: json['id'] as String,
        name: json['name'] as String,
        category: json['category'] as String,
        supplier: json['supplier'] as String,
        costPrice: (json['costPrice'] as num).toDouble(),
        wholesalePrice: (json['wholesalePrice'] as num).toDouble(),
        moq: (json['moq'] as num).toInt(),
        stock: (json['stock'] as num).toInt(),
      );
}

class UsedPart {
  final String inventoryItemId;
  final int quantity;
  final String name;
  final double sellingPrice;

  const UsedPart({
    required this.inventoryItemId,
    required this.quantity,
    required this.name,
    required this.sellingPrice,
  });

  Map<String, dynamic> toJson() => {
        'inventoryItemId': inventoryItemId,
        'quantity': quantity,
        'name': name,
        'sellingPrice': sellingPrice,
      };

  factory UsedPart.fromJson(Map<String, dynamic> json) => UsedPart(
        inventoryItemId: json['inventoryItemId'] as String,
        quantity: (json['quantity'] as num).toInt(),
        name: json['name'] as String,
        sellingPrice: (json['sellingPrice'] as num).toDouble(),
      );
}

class Order {
  final String id;
  final Customer customer;
  final String? brand;
  final String deviceModel;
  final String? imei;
  final String? serialNo;
  final String issueDescription;
  final double estimatedCost;
  final double advancePayment;
  final OrderStatus status;
  final JobPriority priority;
  final DateTime createdAt;
  final DateTime updatedAt;
  // Deprecated: photoUrl kept for backward compatibility; use photoUrls instead
  final String? photoUrl;
  final List<String> photoUrls;
  final String? technicianId;
  final List<UsedPart> usedParts;
  final double laborCost;

  const Order({
    required this.id,
    required this.customer,
    this.brand,
    required this.deviceModel,
    this.imei,
    this.serialNo,
    required this.issueDescription,
    required this.estimatedCost,
    required this.advancePayment,
    required this.status,
    required this.priority,
    required this.createdAt,
    required this.updatedAt,
    this.photoUrl,
    this.photoUrls = const [],
    this.technicianId,
    this.usedParts = const [],
    this.laborCost = 0,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'customer': customer.toJson(),
        'brand': brand,
        'deviceModel': deviceModel,
        'imei': imei,
        'serialNo': serialNo,
        'issueDescription': issueDescription,
        'estimatedCost': estimatedCost,
        'advancePayment': advancePayment,
        'status': status.name,
        'priority': priority.name,
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'photoUrl': photoUrl,
        'photoUrls': photoUrls,
        'technicianId': technicianId,
        'usedParts': usedParts.map((e) => e.toJson()).toList(),
        'laborCost': laborCost,
      };

  factory Order.fromJson(Map<String, dynamic> json) => Order(
        id: json['id'] as String,
        customer: Customer.fromJson(json['customer'] as Map<String, dynamic>),
        brand: json['brand'] as String?,
        deviceModel: json['deviceModel'] as String,
        imei: json['imei'] as String?,
        serialNo: json['serialNo'] as String?,
        issueDescription: json['issueDescription'] as String,
        estimatedCost: (json['estimatedCost'] as num).toDouble(),
        advancePayment: (json['advancePayment'] as num).toDouble(),
        status: OrderStatus.values.firstWhere((e) => e.name == json['status']),
        priority: JobPriority.values.firstWhere((e) => e.name == json['priority']),
        createdAt: DateTime.parse(json['createdAt'] as String),
        updatedAt: DateTime.parse(json['updatedAt'] as String),
        photoUrl: json['photoUrl'] as String?,
        photoUrls: ((json['photoUrls'] as List?)?.cast<String>())
            ?? (json['photoUrl'] != null ? [json['photoUrl'] as String] : <String>[]),
        technicianId: json['technicianId'] as String?,
        usedParts: ((json['usedParts'] as List?) ?? [])
            .map((e) => UsedPart.fromJson(e as Map<String, dynamic>))
            .toList(),
        laborCost: (json['laborCost'] as num?)?.toDouble() ?? 0,
      );
}

class UserProfile {
  final String id;
  final String name;
  final String? email;
  final String? phone;
  final String? businessName;
  final String? businessAddress;
  final String? businessPhone;
  final String? businessEmail;
  final String? gstNumber;
  final String? profileImagePath;
  final String? businessLogoPath;
  final Map<String, String>? businessHours;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UserProfile({
    required this.id,
    required this.name,
    this.email,
    this.phone,
    this.businessName,
    this.businessAddress,
    this.businessPhone,
    this.businessEmail,
    this.gstNumber,
    this.profileImagePath,
    this.businessLogoPath,
    this.businessHours,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'email': email,
        'phone': phone,
        'businessName': businessName,
        'businessAddress': businessAddress,
        'businessPhone': businessPhone,
        'businessEmail': businessEmail,
        'gstNumber': gstNumber,
        'profileImagePath': profileImagePath,
        'businessLogoPath': businessLogoPath,
        'businessHours': businessHours,
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
      };

  factory UserProfile.fromJson(Map<String, dynamic> json) => UserProfile(
        id: json['id'] as String,
        name: json['name'] as String,
        email: json['email'] as String?,
        phone: json['phone'] as String?,
        businessName: json['businessName'] as String?,
        businessAddress: json['businessAddress'] as String?,
        businessPhone: json['businessPhone'] as String?,
        businessEmail: json['businessEmail'] as String?,
        gstNumber: json['gstNumber'] as String?,
        profileImagePath: json['profileImagePath'] as String?,
        businessLogoPath: json['businessLogoPath'] as String?,
        businessHours: json['businessHours'] != null
            ? Map<String, String>.from(json['businessHours'] as Map)
            : null,
        createdAt: DateTime.parse(json['createdAt'] as String? ?? DateTime.now().toIso8601String()),
        updatedAt: DateTime.parse(json['updatedAt'] as String? ?? DateTime.now().toIso8601String()),
      );

  UserProfile copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? businessName,
    String? businessAddress,
    String? businessPhone,
    String? businessEmail,
    String? gstNumber,
    String? profileImagePath,
    String? businessLogoPath,
    Map<String, String>? businessHours,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserProfile(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      businessName: businessName ?? this.businessName,
      businessAddress: businessAddress ?? this.businessAddress,
      businessPhone: businessPhone ?? this.businessPhone,
      businessEmail: businessEmail ?? this.businessEmail,
      gstNumber: gstNumber ?? this.gstNumber,
      profileImagePath: profileImagePath ?? this.profileImagePath,
      businessLogoPath: businessLogoPath ?? this.businessLogoPath,
      businessHours: businessHours ?? this.businessHours,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class AppSettings {
  final bool isDarkMode;
  final String language;
  final String currency;
  final bool notificationsEnabled;
  final bool autoBackup;
  final String dateFormat;
  final String timeFormat;
  final double taxRate;
  final bool biometricEnabled;
  final int autoLogoutMinutes;

  const AppSettings({
    this.isDarkMode = true,
    this.language = 'English',
    this.currency = '₹ INR',
    this.notificationsEnabled = true,
    this.autoBackup = true,
    this.dateFormat = 'DD/MM/YYYY',
    this.timeFormat = '24h',
    this.taxRate = 18.0,
    this.biometricEnabled = false,
    this.autoLogoutMinutes = 30,
  });

  Map<String, dynamic> toJson() => {
        'isDarkMode': isDarkMode,
        'language': language,
        'currency': currency,
        'notificationsEnabled': notificationsEnabled,
        'autoBackup': autoBackup,
        'dateFormat': dateFormat,
        'timeFormat': timeFormat,
        'taxRate': taxRate,
        'biometricEnabled': biometricEnabled,
        'autoLogoutMinutes': autoLogoutMinutes,
      };

  factory AppSettings.fromJson(Map<String, dynamic> json) => AppSettings(
        isDarkMode: json['isDarkMode'] as bool? ?? true,
        language: json['language'] as String? ?? 'English',
        currency: json['currency'] as String? ?? '₹ INR',
        notificationsEnabled: json['notificationsEnabled'] as bool? ?? true,
        autoBackup: json['autoBackup'] as bool? ?? true,
        dateFormat: json['dateFormat'] as String? ?? 'DD/MM/YYYY',
        timeFormat: json['timeFormat'] as String? ?? '24h',
        taxRate: (json['taxRate'] as num?)?.toDouble() ?? 18.0,
        biometricEnabled: json['biometricEnabled'] as bool? ?? false,
        autoLogoutMinutes: json['autoLogoutMinutes'] as int? ?? 30,
      );

  AppSettings copyWith({
    bool? isDarkMode,
    String? language,
    String? currency,
    bool? notificationsEnabled,
    bool? autoBackup,
    String? dateFormat,
    String? timeFormat,
    double? taxRate,
    bool? biometricEnabled,
    int? autoLogoutMinutes,
  }) {
    return AppSettings(
      isDarkMode: isDarkMode ?? this.isDarkMode,
      language: language ?? this.language,
      currency: currency ?? this.currency,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      autoBackup: autoBackup ?? this.autoBackup,
      dateFormat: dateFormat ?? this.dateFormat,
      timeFormat: timeFormat ?? this.timeFormat,
      taxRate: taxRate ?? this.taxRate,
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
      autoLogoutMinutes: autoLogoutMinutes ?? this.autoLogoutMinutes,
    );
  }
}


