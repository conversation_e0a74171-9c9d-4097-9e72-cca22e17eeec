

import React, { useState } from 'react';
import { useApp } from '../../context/AppContext';
import type { InventoryItem } from '../../types';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { PlusCircle, AlertTriangle, Trash2, Tag, Archive, DollarSign, Save } from 'lucide-react';
import Modal from '../ui/Modal';
import Input from '../ui/Input';

const Item: React.FC<{ item: InventoryItem }> = ({ item }) => {
  const { deleteInventoryItem } = useApp();
  const isLowStock = item.quantity <= item.lowStockThreshold;
  return (
    <Card className="mb-4">
      <div className="flex justify-between items-start">
        <div className="flex-grow">
          <p className="font-bold text-gray-800">{item.name}</p>
          <p className="text-sm text-gray-500">Selling Price: ₹{item.sellingPrice}</p>
        </div>
        <div className="flex items-center gap-2">
            <div className="text-right">
                <p className={`font-bold text-2xl ${isLowStock ? 'text-red-500' : 'text-gray-800'}`}>{item.quantity}</p>
                <p className="text-xs text-gray-500">in stock</p>
            </div>
            <Button variant="ghost" size="sm" className="!text-red-600 hover:!bg-red-50 !p-2 self-center" onClick={() => deleteInventoryItem(item.id)}>
                <Trash2 size={16} />
            </Button>
        </div>
      </div>
      {isLowStock && (
        <div className="mt-3 flex items-center gap-2 text-red-600 bg-red-50 p-2 rounded-lg text-sm">
          <AlertTriangle size={16} />
          <span>Low stock warning</span>
        </div>
      )}
    </Card>
  );
}

const InventoryScreen: React.FC = () => {
  const { inventory, addInventoryItem, setToast, filterQuery } = useApp();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [newItemData, setNewItemData] = useState({
      name: '',
      quantity: '',
      costPrice: '',
      sellingPrice: '',
      lowStockThreshold: '5',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setNewItemData(prev => ({ ...prev, [name]: value }));
  };

  const handleAddItem = (e: React.FormEvent) => {
      e.preventDefault();
      const { name, quantity, costPrice, sellingPrice, lowStockThreshold } = newItemData;
      if (!name.trim() || !quantity || !sellingPrice) {
          setToast("Please fill Name, Quantity, and Selling Price.");
          return;
      }
      addInventoryItem({
          name,
          quantity: parseInt(quantity, 10),
          costPrice: parseFloat(costPrice) || 0,
          sellingPrice: parseFloat(sellingPrice),
          lowStockThreshold: parseInt(lowStockThreshold, 10) || 0,
      });
      setToast(`${name} added to inventory.`);
      setIsAddModalOpen(false);
      setNewItemData({ name: '', quantity: '', costPrice: '', sellingPrice: '', lowStockThreshold: '5' });
  };
  
  const filteredInventory = inventory.filter(item => {
    if (!filterQuery) return true;
    return item.name.toLowerCase().includes(filterQuery.toLowerCase());
  });


  return (
    <div className="space-y-6">
       <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-800">Inventory</h2>
        <Button size="sm" onClick={() => setIsAddModalOpen(true)}>
          <PlusCircle size={16} />
          Add Item
        </Button>
      </div>
      
      <Modal isOpen={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} title="Add New Inventory Item">
          <form onSubmit={handleAddItem} className="space-y-4">
              <Input
                  label="Item Name"
                  name="name"
                  value={newItemData.name}
                  onChange={handleInputChange}
                  icon={<Tag size={18} />}
                  placeholder="e.g., iPhone 13 Screen"
                  required
              />
              <div className="grid grid-cols-2 gap-4">
                <Input
                    label="Quantity"
                    name="quantity"
                    type="number"
                    value={newItemData.quantity}
                    onChange={handleInputChange}
                    icon={<Archive size={18} />}
                    placeholder="e.g., 10"
                    required
                />
                <Input
                    label="Low Stock Threshold"
                    name="lowStockThreshold"
                    type="number"
                    value={newItemData.lowStockThreshold}
                    onChange={handleInputChange}
                    icon={<AlertTriangle size={18} />}
                    placeholder="e.g., 5"
                    required
                />
              </div>
               <div className="grid grid-cols-2 gap-4">
                  <Input
                      label="Cost Price (₹)"
                      name="costPrice"
                      type="number"
                      step="0.01"
                      value={newItemData.costPrice}
                      onChange={handleInputChange}
                      icon={<DollarSign size={18} />}
                      placeholder="e.g., 5000"
                  />
                  <Input
                      label="Selling Price (₹)"
                      name="sellingPrice"
                      type="number"
                      step="0.01"
                      value={newItemData.sellingPrice}
                      onChange={handleInputChange}
                      icon={<DollarSign size={18} />}
                      placeholder="e.g., 7500"
                      required
                  />
               </div>

              <div className="flex justify-end pt-4">
                  <Button type="submit">
                      <Save size={18} />
                      Save Item
                  </Button>
              </div>
          </form>
      </Modal>

      <div>
        {filteredInventory.length > 0 ? (
          filteredInventory.map(item => <Item key={item.id} item={item} />)
        ) : (
          <Card className="text-center text-gray-500 py-8">
            {filterQuery ? `No items found for "${filterQuery}"` : 'No inventory items found.'}
          </Card>
        )}
      </div>
    </div>
  );
};

export default InventoryScreen;