
import React from 'react';
import { useApp } from '../../context/AppContext';
import Card from '../ui/Card';
import StatusTracker from '../ui/StatusTracker';
import { OrderStatus } from '../../types';
import { Smartphone, ShieldQuestion, Store, Phone } from 'lucide-react';
import Button from '../ui/Button';

interface PublicOrderViewScreenProps {
    orderId: string;
}

const PublicOrderViewScreen: React.FC<PublicOrderViewScreenProps> = ({ orderId }) => {
    const { orders, shopProfile } = useApp();
    const order = orders.find(o => o.id === orderId);

    if (!order) {
        return (
            <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
                <Card className="text-center p-8 w-full max-w-md">
                    <ShieldQuestion className="mx-auto h-16 w-16 text-red-400" />
                    <h2 className="mt-4 text-2xl font-bold text-gray-800">Order Not Found</h2>
                    <p className="mt-2 text-gray-500">The link may be invalid or the order may have been removed. Please contact the shop for assistance.</p>
                </Card>
            </div>
        );
    }
    
    const statusFlow = [
        OrderStatus.Received,
        OrderStatus.Diagnosing,
        OrderStatus.RepairInProgress,
        OrderStatus.QualityCheck,
        OrderStatus.ReadyForDelivery,
        OrderStatus.Delivered,
    ];
    
    const amountDue = order.estimatedCost - order.advancePayment;

    return (
        <div className="min-h-screen bg-gray-50 font-sans">
             <header className="bg-accent text-white p-4 shadow-md">
                <div className="max-w-2xl mx-auto flex items-center gap-4">
                    {shopProfile.avatarUrl ? (
                      <img src={shopProfile.avatarUrl} alt={shopProfile.name} className="w-12 h-12 rounded-full object-cover ring-2 ring-white/50"/>
                    ) : (
                      <div className="w-12 h-12 rounded-full flex items-center justify-center bg-white/20">
                          <Store className="w-7 h-7 text-white"/>
                      </div>
                    )}
                    <div>
                        <p className="text-sm">Repair Status for</p>
                        <h1 className="text-xl font-bold">{shopProfile.name}</h1>
                    </div>
                </div>
            </header>

            <main className="p-4 max-w-2xl mx-auto">
                <div className="space-y-6">
                    <Card>
                        <div className="text-center py-4">
                            <Smartphone size={48} className="mx-auto text-gray-400 mb-2"/>
                            <h2 className="text-2xl font-bold text-gray-800">{order.brand} {order.deviceModel}</h2>
                            <p className="text-sm text-gray-500 mt-1">Order ID: {order.id}</p>
                        </div>
                    </Card>

                    <Card>
                        <h3 className="text-lg font-bold text-gray-700 mb-4">Current Progress</h3>
                        <StatusTracker currentStatus={order.status} statusFlow={statusFlow} />
                    </Card>
                    
                    <Card>
                        <h3 className="text-lg font-bold text-gray-700 mb-4">Financials</h3>
                        <div className="space-y-2 text-sm">
                             <div className="flex justify-between font-bold text-base text-accent border-b pb-2 mb-2">
                                 <span>Total Estimated Cost</span>
                                 <span>₹{order.estimatedCost.toLocaleString()}</span>
                             </div>
                             <div className="flex justify-between">
                                 <span className="text-green-600">Advance Paid</span>
                                 <span className="font-medium text-green-600">- ₹{order.advancePayment.toLocaleString()}</span>
                             </div>
                         </div>
                        <div className="mt-4 bg-accent-light text-accent text-center p-3 rounded-lg">
                            <p className="text-sm font-semibold">Amount Due</p>
                            <p className="text-2xl font-bold">₹{amountDue.toLocaleString()}</p>
                        </div>
                    </Card>

                    <Card>
                        <h3 className="text-lg font-bold text-gray-700 mb-2">Need Help?</h3>
                        <p className="text-sm text-gray-600 mb-4">If you have any questions about your repair, please contact us.</p>
                        <a href={`tel:${shopProfile.phone}`}>
                            <Button className="w-full" variant="secondary">
                                <Phone size={16} /> Call {shopProfile.name}
                            </Button>
                        </a>
                    </Card>
                </div>
            </main>
        </div>
    );
};

export default PublicOrderViewScreen;
