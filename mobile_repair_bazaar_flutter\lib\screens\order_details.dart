import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
import 'package:printing/printing.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import '../utils/links.dart';

import '../app_state.dart';
import '../models.dart';
import '../widgets/badges.dart';

class OrderDetailsScreen extends StatelessWidget {
  final String orderId;
  const OrderDetailsScreen({super.key, required this.orderId});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(builder: (_, state, __) {
      final order = state.getOrderById(orderId);
      if (order == null) {
        return const Scaffold(body: Center(child: Text('Order not found')));
      }

      final flow = const [
        OrderStatus.received,
        OrderStatus.diagnosing,
        OrderStatus.repairInProgress,
        OrderStatus.qualityCheck,
        OrderStatus.readyForDelivery,
        OrderStatus.delivered,
      ];
      final currentIndex = flow.indexOf(order.status);
      final next = currentIndex >= 0 && currentIndex < flow.length - 1 ? flow[currentIndex + 1] : null;

      final partsTotal = order.usedParts.fold<double>(0, (s, p) => s + p.sellingPrice * p.quantity);
      final calculatedTotal = partsTotal + order.laborCost;
      final amountDue = order.estimatedCost - order.advancePayment;

      return Scaffold(
        appBar: AppBar(title: const Text('Order Details')),
        body: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                  Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                    const Text('Job Details', style: TextStyle(fontWeight: FontWeight.bold)),
                    Row(children: [StatusBadge(status: order.status), const SizedBox(width: 6), PriorityBadge(priority: order.priority)])
                  ]),
                  const SizedBox(height: 8),
                  Text('Order ID: ${order.id}', style: const TextStyle(color: Colors.grey)),
                  const SizedBox(height: 8),
                  ListTile(
                    contentPadding: EdgeInsets.zero,
                    leading: CircleAvatar(
                      radius: 22,
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      backgroundImage: (order.customer.avatarUrl != null && order.customer.avatarUrl!.isNotEmpty)
                          ? FileImage(File(order.customer.avatarUrl!))
                          : null,
                      child: (order.customer.avatarUrl == null || order.customer.avatarUrl!.isEmpty)
                          ? Text(
                              order.customer.name.isNotEmpty ? order.customer.name[0].toUpperCase() : '?',
                              style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                            )
                          : null,
                    ),
                    title: Text(order.customer.name),
                    subtitle: Text(order.customer.phone),
                  ),
                ]),
              ),
            ),
            const SizedBox(height: 12),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                  const Text('Device Information', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  Text('Device: ${order.brand ?? ''} ${order.deviceModel}'.trim()),
                  if (order.imei != null || order.serialNo != null) ...[
                    const SizedBox(height: 4),
                    Text('IMEI / S/N: ${order.imei ?? order.serialNo}')
                  ],
                  const SizedBox(height: 8),
                  Text('Issue: ${order.issueDescription}')
                ]),
              ),
            ),
            if (order.photoUrls.isNotEmpty) ...[
              const SizedBox(height: 12),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                    const Text('Device Photos', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 140,
                      child: ListView.separated(
                        scrollDirection: Axis.horizontal,
                        itemBuilder: (_, i) => ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: Image.file(
                            File(order.photoUrls[i]),
                            width: 180,
                            height: 140,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) => Container(
                              width: 180,
                              height: 140,
                              decoration: BoxDecoration(
                                color: Colors.grey.shade200,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Icon(Icons.photo_library, size: 50, color: Colors.grey),
                            ),
                          ),
                        ),
                        separatorBuilder: (_, __) => const SizedBox(width: 8),
                        itemCount: order.photoUrls.length,
                      ),
                    ),
                  ]),
                ),
              ),
            ],
            const SizedBox(height: 12),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                  const Text('Financial Summary', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  _kv('Parts Total', '₹${partsTotal.toStringAsFixed(0)}'),
                  _kv('Labor Cost', '₹${order.laborCost.toStringAsFixed(0)}'),
                  const Divider(),
                  _kv('Calculated Total', '₹${calculatedTotal.toStringAsFixed(0)}'),
                  const Divider(),
                  _kv('Final Estimated Cost', '₹${order.estimatedCost.toStringAsFixed(0)}', bold: true),
                  _kv('Advance Paid', '- ₹${order.advancePayment.toStringAsFixed(0)}', color: Colors.green),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(color: Theme.of(context).colorScheme.primary.withOpacity(.1), borderRadius: BorderRadius.circular(12)),
                    child: Column(children: [
                      const Text('Amount Due', style: TextStyle(fontWeight: FontWeight.w600)),
                      Text('₹${amountDue.toStringAsFixed(0)}', style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold))
                    ]),
                  )
                ]),
              ),
            ),
            const SizedBox(height: 12),
            Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _shareOrderPDF(context, order),
                      icon: const Icon(Icons.picture_as_pdf),
                      label: const Text('Share PDF'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _shareOrderWhatsApp(order),
                      icon: const Icon(Icons.message),
                      label: const Text('WhatsApp'),
                    ),
                  ),
                ],
              ),
            const SizedBox(height: 12),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                  const Text('Job Progress', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  for (int i = 0; i < flow.length; i++) _statusStep(context, flow[i], i, currentIndex)
                ]),
              ),
            ),
            const SizedBox(height: 12),
            if (order.status != OrderStatus.delivered && order.status != OrderStatus.cancelled) ...[
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  for (final s in flow)
                    ChoiceChip(
                      label: Text(_statusLabel(s)),
                      selected: order.status == s,
                      onSelected: (selected) {
                        if (selected) context.read<AppState>().updateOrderStatus(order.id, s);
                      },
                    ),
                ],
              ),
              const SizedBox(height: 12),
              FilledButton.icon(
                onPressed: next == null ? null : () => context.read<AppState>().updateOrderStatus(order.id, next),
                icon: const Icon(Icons.arrow_right_alt),
                label: Text(next == null ? 'Completed' : _nextText(next)),
              ),
            ],
            if (order.status != OrderStatus.delivered && order.status != OrderStatus.cancelled)
              TextButton.icon(
                onPressed: () => context.read<AppState>().updateOrderStatus(order.id, OrderStatus.cancelled),
                icon: const Icon(Icons.cancel_outlined),
                label: const Text('Cancel Order'),
              ),
          ],
        ),
      );
    });
  }

  static String _nextText(OrderStatus status) => switch (status) {
        OrderStatus.diagnosing => 'Start Diagnosis',
        OrderStatus.repairInProgress => 'Begin Repair',
        OrderStatus.qualityCheck => 'Complete Quality Check',
        OrderStatus.readyForDelivery => 'Mark as Ready for Delivery',
        OrderStatus.delivered => 'Mark as Delivered',
        _ => 'Proceed',
      };

  Widget _kv(String k, String v, {bool bold = false, Color? color}) => Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(k, style: TextStyle(color: Colors.grey.shade700)),
          Text(v, style: TextStyle(fontWeight: bold ? FontWeight.bold : FontWeight.w600, color: color)),
        ],
      );

  Widget _statusStep(BuildContext context, OrderStatus s, int i, int current) {
    final active = current == i;
    final done = current > i;
    final cancelled = s == OrderStatus.cancelled && active;
    Color bg;
    Color fg;
    if (cancelled) {
      bg = Colors.red.shade100;
      fg = Colors.red.shade700;
    } else if (done || current == flowEndIndex) {
      bg = Colors.green.shade100;
      fg = Colors.green.shade700;
    } else if (active) {
      bg = Theme.of(context).colorScheme.primary.withOpacity(.15);
      fg = Theme.of(context).colorScheme.primary;
    } else {
      bg = Colors.grey.shade200;
      fg = Colors.grey.shade700;
    }
    final label = _statusLabel(s);
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(color: bg, borderRadius: BorderRadius.circular(12)),
      child: Row(children: [Icon(_statusIcon(s), color: fg), const SizedBox(width: 8), Text(label, style: TextStyle(color: fg, fontWeight: FontWeight.w600))]),
    );
  }

  static int get flowEndIndex => 5;
  static IconData _statusIcon(OrderStatus s) => switch (s) {
        OrderStatus.received => Icons.move_to_inbox,
        OrderStatus.diagnosing => Icons.search,
        OrderStatus.repairInProgress => Icons.build,
        OrderStatus.qualityCheck => Icons.verified,
        OrderStatus.readyForDelivery => Icons.local_shipping,
        OrderStatus.delivered => Icons.check_circle,
        OrderStatus.cancelled => Icons.cancel,
      };
  static String _statusLabel(OrderStatus s) => switch (s) {
        OrderStatus.received => 'Device Received',
        OrderStatus.diagnosing => 'Diagnosing Issue',
        OrderStatus.repairInProgress => 'Repair in Progress',
        OrderStatus.qualityCheck => 'Quality Check',
        OrderStatus.readyForDelivery => 'Ready for Delivery',
        OrderStatus.delivered => 'Delivered',
        OrderStatus.cancelled => 'Cancelled',
      };

  Future<void> _shareOrderPDF(BuildContext context, Order order) async {
    try {
      final doc = pw.Document();
      final rupee = String.fromCharCode(8377);
      
      // Build PDF content
      List<pw.Widget> pdfWidgets = [
        pw.Text('Order Details - ${order.id}', style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold)),
        pw.SizedBox(height: 16),
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Expanded(
              flex: 2,
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text('Order ID: ${order.id}', style: pw.TextStyle(fontSize: 12)),
                  pw.SizedBox(height: 4),
                  pw.Text('Customer: ${order.customer.name}', style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold)),
                  pw.Text('Phone: ${order.customer.phone}', style: pw.TextStyle(fontSize: 12)),
                  if (order.customer.address != null) pw.Text('Address: ${order.customer.address}', style: pw.TextStyle(fontSize: 12)),
                  pw.SizedBox(height: 8),
                  pw.Text('Device: ${(order.brand ?? '')} ${order.deviceModel}', style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold)),
                  pw.Text('Issue: ${order.issueDescription}', style: pw.TextStyle(fontSize: 12)),
                  pw.Text('Status: ${_statusLabel(order.status)}', style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold)),
                  pw.SizedBox(height: 8),
                  pw.Container(
                    padding: const pw.EdgeInsets.all(8),
                    decoration: pw.BoxDecoration(
                      border: pw.Border.all(color: PdfColors.grey),
                      borderRadius: pw.BorderRadius.circular(4),
                    ),
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text('Financial Summary', style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold)),
                        pw.SizedBox(height: 4),
                        pw.Text('Estimated Cost: $rupee${order.estimatedCost.toStringAsFixed(0)}', style: pw.TextStyle(fontSize: 12)),
                        pw.Text('Advance Payment: $rupee${order.advancePayment.toStringAsFixed(0)}', style: pw.TextStyle(fontSize: 12)),
                        pw.Text('Amount Due: $rupee${(order.estimatedCost - order.advancePayment).toStringAsFixed(0)}', style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold)),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            if (order.photoUrl != null) ...[
              pw.SizedBox(width: 16),
              pw.Expanded(
                flex: 1,
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text('Device Photo', style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold)),
                    pw.SizedBox(height: 4),
                    pw.Image(
                      pw.MemoryImage(await File(order.photoUrl!).readAsBytes()),
                      width: 120,
                      height: 120,
                      fit: pw.BoxFit.cover,
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
        pw.SizedBox(height: 16),
        pw.Container(
          padding: const pw.EdgeInsets.all(8),
          decoration: pw.BoxDecoration(
            color: PdfColors.grey100,
            borderRadius: pw.BorderRadius.circular(4),
          ),
          child: pw.Text(
            'Generated on: ${DateTime.now().toString().split('.')[0]}',
            style: pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
          ),
        ),
      ];
      
      doc.addPage(
        pw.Page(
          build: (pw.Context ctx) => pw.Padding(
            padding: const pw.EdgeInsets.all(20),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: pdfWidgets,
            ),
          ),
        ),
      );

      final bytes = await doc.save();
      final dir = await getTemporaryDirectory();
      final filePath = '${dir.path}/OrderDetails-${order.id}.pdf';
      final out = File(filePath);
      await out.writeAsBytes(bytes, flush: true);

      await Share.shareXFiles([XFile(out.path)], text: 'Order Details ${order.id}');
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error generating PDF: $e')));
      }
    }
  }

  Future<void> _shareOrderWhatsApp(Order order) async {
    try {
      final amountDue = order.estimatedCost - order.advancePayment;
      final message = 'Hello ${order.customer.name},\n\n'
          'Your order status update:\n\n'
          'Order ID: ${order.id}\n'
          'Device: ${(order.brand ?? '')} ${order.deviceModel}\n'
          'Current Status: ${_statusLabel(order.status)}\n'
          'Issue: ${order.issueDescription}\n\n'
          'Estimated Cost: ₹${order.estimatedCost.toStringAsFixed(0)}\n'
          'Advance Paid: ₹${order.advancePayment.toStringAsFixed(0)}\n'
          'Amount Due: ₹${amountDue.toStringAsFixed(0)}\n\n'
          'Thank you for choosing our service!';
      
      await launchWhatsAppText(order.customer.phone, message);
    } catch (e) {
      // Handle error silently
    }
  }
}


