
import React, { createContext, useContext, ReactNode } from 'react';
import useAppData from '../hooks/useAppData';

type AppContextType = ReturnType<typeof useAppData>;

const AppContext = createContext<AppContextType | null>(null);

export const AppContextProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const appData = useAppData();
  
  const [theme] = appData.theme;

  return (
    <AppContext.Provider value={appData}>
      <div className={theme}>
        {children}
      </div>
    </AppContext.Provider>
  );
};

export const useApp = (): AppContextType => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppContextProvider');
  }
  return context;
};
