import React from 'react';
import { OrderStatus } from '../../types';

interface StatusBadgeProps {
  status: OrderStatus;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  const statusStyles: { [key in OrderStatus]: string } = {
    [OrderStatus.Received]: 'bg-gray-200 text-gray-800',
    [OrderStatus.Diagnosing]: 'bg-orange-100 text-orange-800',
    [OrderStatus.RepairInProgress]: 'bg-blue-100 text-blue-800',
    [OrderStatus.QualityCheck]: 'bg-indigo-100 text-indigo-800',
    [OrderStatus.ReadyForDelivery]: 'bg-green-100 text-green-800',
    [OrderStatus.Delivered]: 'bg-purple-100 text-purple-800',
    [OrderStatus.Cancelled]: 'bg-red-100 text-red-800',
  };

  return (
    <span className={`px-2.5 py-0.5 text-xs font-semibold rounded-full ${statusStyles[status] || statusStyles[OrderStatus.Received]}`}>
      {status}
    </span>
  );
};

export default StatusBadge;