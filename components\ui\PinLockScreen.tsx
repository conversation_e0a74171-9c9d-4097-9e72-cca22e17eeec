
import React, { useState, useEffect } from 'react';
import { KeyRound, Delete } from 'lucide-react';

interface PinLockScreenProps {
  appPin: string | null;
  unlockApp: (pin: string) => boolean;
  setPinAndUnlock: (pin: string) => void;
}

const PinLockScreen: React.FC<PinLockScreenProps> = ({ appPin, unlockApp, setPinAndUnlock }) => {
  const [pin, setPin] = useState('');
  const [isConfirming, setIsConfirming] = useState(false);
  const [firstPin, setFirstPin] = useState('');
  const [error, setError] = useState('');
  
  const mode = appPin ? 'unlock' : 'setup';
  const title = mode === 'unlock' 
    ? 'Enter your PIN' 
    : isConfirming ? 'Confirm your PIN' : 'Create a 4-digit PIN';

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(''), 1500);
      return () => clearTimeout(timer);
    }
  }, [error]);

  useEffect(() => {
    if (pin.length === 4) {
      if (mode === 'unlock') {
        const success = unlockApp(pin);
        if (!success) {
          setError('Incorrect PIN. Try again.');
          // Animate shake
          const pinDots = document.getElementById('pin-dots');
          if (pinDots) {
            pinDots.classList.add('animate-shake');
            setTimeout(() => pinDots.classList.remove('animate-shake'), 500);
          }
          setPin('');
        }
      } else { // setup mode
        if (isConfirming) {
          if (pin === firstPin) {
            setPinAndUnlock(pin);
          } else {
            setError("PINs don't match. Please start over.");
            setPin('');
            setFirstPin('');
            setIsConfirming(false);
          }
        } else {
          setFirstPin(pin);
          setIsConfirming(true);
          setPin('');
        }
      }
    }
  }, [pin, appPin, unlockApp, setPinAndUnlock, isConfirming, firstPin, mode]);

  const handlePinClick = (num: string) => {
    if (pin.length < 4) {
      setPin(pin + num);
    }
  };

  const handleDelete = () => {
    setPin(pin.slice(0, -1));
  };
  
  const PinDot = ({ filled }: { filled: boolean }) => (
    <div className={`w-4 h-4 rounded-full border-2 transition-all duration-200 ${filled ? 'bg-accent border-accent' : 'border-gray-400 bg-white'}`} />
  );

  const NumpadButton = ({ children, onClick }: { children: React.ReactNode, onClick: () => void }) => (
     <button onClick={onClick} className="text-3xl font-light text-gray-800 rounded-full h-20 w-20 flex items-center justify-center hover:bg-gray-100 transition-colors active:bg-gray-200">
        {children}
     </button>
  );
  
  const animationKeyframes = `
    @keyframes shake {
      10%, 90% { transform: translate3d(-1px, 0, 0); }
      20%, 80% { transform: translate3d(2px, 0, 0); }
      30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
      40%, 60% { transform: translate3d(4px, 0, 0); }
    }
    .animate-shake {
      animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
    }
  `;

  return (
    <div className="fixed inset-0 z-[100] bg-gray-50 flex flex-col items-center justify-center animate-fade-in-down">
        <style>{animationKeyframes}</style>
        <div className="flex flex-col items-center text-center p-8 w-full max-w-xs">
            <div className="bg-accent-light text-accent p-4 rounded-full mb-4">
                <KeyRound size={32} />
            </div>
            <h2 className="text-2xl font-bold text-gray-800">{title}</h2>
            {error ? (
                <p className="text-red-500 font-semibold h-6 mt-2">{error}</p>
            ) : (
                <p className="text-gray-500 h-6 mt-2">Enter 4 digits to continue</p>
            )}

            <div id="pin-dots" className="flex gap-4 my-8">
                <PinDot filled={pin.length >= 1} />
                <PinDot filled={pin.length >= 2} />
                <PinDot filled={pin.length >= 3} />
                <PinDot filled={pin.length >= 4} />
            </div>

            <div className="grid grid-cols-3 gap-4">
                {[1, 2, 3, 4, 5, 6, 7, 8, 9].map(num => (
                    <NumpadButton key={num} onClick={() => handlePinClick(String(num))}>{num}</NumpadButton>
                ))}
                <div />
                <NumpadButton onClick={() => handlePinClick('0')}>0</NumpadButton>
                <NumpadButton onClick={handleDelete}><Delete size={28} /></NumpadButton>
            </div>
        </div>
    </div>
  );
};

export default PinLockScreen;
