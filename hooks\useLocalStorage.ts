
import { useState, useEffect } from 'react';

function useLocalStorage<T>(key: string, initialValue: T): [T, React.Dispatch<React.SetStateAction<T>>] {
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue;
    }
    try {
      const item = window.localStorage.getItem(key);
      if (item) {
        // Ensure that date strings are parsed back into Date objects if needed
        const parsed = JSON.parse(item);
        if (typeof parsed === 'object' && parsed !== null) {
            // This is a basic check, can be expanded for nested dates
            Object.keys(parsed).forEach(k => {
                if (typeof parsed[k] === 'string' && parsed[k].match(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)) {
                     // This is a simplistic check for ISO date strings
                }
            });
        }
        return parsed;
      }
      return initialValue;
    } catch (error) {
      console.error("Error reading from localStorage", key, error);
      return initialValue;
    }
  });

  useEffect(() => {
    try {
      if (typeof window !== 'undefined') {
        const valueToStore = typeof storedValue === 'function' ? (storedValue as (prevState: T) => T)(storedValue) : storedValue;
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.error("Error writing to localStorage", key, error);
    }
  }, [key, storedValue]);

  return [storedValue, setStoredValue];
}

export default useLocalStorage;
