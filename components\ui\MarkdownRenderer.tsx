import React from 'react';

// A simple function to sanitize content for dangerouslySetInnerHTML
const createMarkup = (htmlContent: string) => {
    // In a real app, you might use a library like DOMPurify here.
    // For this case, we trust the Gemini API output and simple replacements.
    return { __html: htmlContent };
};

const MarkdownRenderer: React.FC<{ content: string }> = ({ content }) => {
    const renderContent = () => {
        if (!content) return null;
        
        // Process paragraphs, handling lists within them
        const paragraphs = content.split('\n\n');

        return paragraphs.map((paragraph, pIndex) => {
            const lines = paragraph.split('\n');
            
            // Check if this block is an unordered list
            if (lines.every(line => line.trim().startsWith('* '))) {
                return (
                    <ul key={pIndex} className="list-disc list-inside space-y-1 my-3 pl-2">
                        {lines.map((item, liIndex) => {
                             const formattedItem = item.trim().substring(2).replace(/\*\*(.*?)\*\*/g, '<strong class="font-bold">$1</strong>');
                             return <li key={liIndex} dangerouslySetInnerHTML={createMarkup(formattedItem)} />;
                        })}
                    </ul>
                );
            }

            // Otherwise, treat as a regular paragraph
            const formattedParagraph = lines.map(line => 
                line.replace(/\*\*(.*?)\*\*/g, '<strong class="font-bold">$1</strong>')
            ).join('<br />');

            return <p key={pIndex} className="my-3" dangerouslySetInnerHTML={createMarkup(formattedParagraph)} />;
        });
    };

    return (
        <div className="text-gray-700 leading-relaxed">
            {renderContent()}
        </div>
    );
};

export default MarkdownRenderer;
