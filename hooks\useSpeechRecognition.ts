
import { useState, useEffect, useRef } from 'react';

// Define the interface for the speech recognition API, which might not be typed
interface ISpeechRecognition extends EventTarget {
  continuous: boolean;
  lang: string;
  interimResults: boolean;
  maxAlternatives: number;
  start(): void;
  stop(): void;
  onresult: (event: any) => void;
  onerror: (event: any) => void;
  onstart: () => void;
  onend: () => void;
}

// Extend the Window interface to include vendor-prefixed speech recognition
declare global {
  interface Window {
    SpeechRecognition: { new(): ISpeechRecognition; };
    webkitSpeechRecognition: { new(): ISpeechRecognition; };
  }
}

const useSpeechRecognition = () => {
    const [isListening, setIsListening] = useState(false);
    const [transcript, setTranscript] = useState('');
    const [error, setError] = useState<string | null>(null);
    const recognitionRef = useRef<ISpeechRecognition | null>(null);

    useEffect(() => {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        if (!SpeechRecognition) {
            setError('Speech recognition is not supported by this browser.');
            return;
        }

        const recognition = new SpeechRecognition();
        recognition.continuous = false;
        recognition.lang = 'en-IN'; // Set to Indian English for better accuracy
        recognition.interimResults = false;

        recognition.onresult = (event: any) => {
            const currentTranscript = event.results[0][0].transcript;
            setTranscript(currentTranscript);
        };

        recognition.onerror = (event: any) => {
            console.error('Speech recognition error:', event.error);
            let errorMessage = `Speech recognition error: ${event.error}`;
            if (event.error === 'not-allowed' || event.error === 'service-not-allowed') {
              errorMessage = "Microphone permission denied. Please enable it in browser settings.";
            }
            setError(errorMessage);
            setIsListening(false);
        };
        
        recognition.onstart = () => {
            setIsListening(true);
            setTranscript('');
            setError(null);
        };

        recognition.onend = () => {
            setIsListening(false);
        };
        
        recognitionRef.current = recognition;

    }, []);

    const startListening = () => {
        if (recognitionRef.current && !isListening) {
            try {
                recognitionRef.current.start();
            } catch(e) {
                console.error("Could not start recognition", e);
                setError("Could not start voice search. Please try again.");
            }
        }
    };
    
    const stopListening = () => {
        if (recognitionRef.current && isListening) {
            recognitionRef.current.stop();
        }
    };

    return {
        isListening,
        transcript,
        error,
        startListening,
        stopListening,
        hasRecognitionSupport: !!(window.SpeechRecognition || window.webkitSpeechRecognition)
    };
};

export default useSpeechRecognition;
