

import React, { useState, useRef, useEffect } from 'react';
import { useApp } from '../../context/AppContext';
import type { Technician } from '../../types';
import { OrderStatus, TechnicianRole } from '../../types';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { PlusCircle, UserCheck, Trash2, ChevronDown, Smartphone, User, Save, Briefcase as BriefcaseIcon, Edit, UploadCloud, Camera as CameraIcon, ImageIcon, Phone, MessageCircle } from 'lucide-react';
import StatusBadge from '../ui/StatusBadge';
import Modal from '../ui/Modal';
import Input from '../ui/Input';
import Select from '../ui/Select';
import ActionSheet from '../ui/ActionSheet';
import CameraModal from '../ui/CameraModal';

const TechnicianJobItem: React.FC<{ job: import('../../types').Order }> = ({ job }) => {
    const { viewOrderDetails } = useApp();
    return (
        <button onClick={() => viewOrderDetails(job.id)} className="w-full text-left p-2.5 my-1.5 bg-white hover:bg-gray-100 rounded-lg shadow-sm transition-all flex items-center gap-4">
            <div className="flex-shrink-0 w-10 h-10 bg-gray-200 rounded-md flex items-center justify-center">
                 {job.photoUrl ? <img src={job.photoUrl} alt={job.deviceModel} className="w-full h-full object-cover rounded-md" /> : <Smartphone size={20} className="text-gray-500"/>}
            </div>
            <div className="flex-grow min-w-0">
                <p className="font-semibold text-gray-800 truncate">{job.deviceModel}</p>
                <p className="text-xs text-gray-500 truncate">{job.customer.name}</p>
            </div>
            <div className="flex-shrink-0">
                <StatusBadge status={job.status} />
            </div>
        </button>
    )
}

const TechnicianItem: React.FC<{ technician: Technician; onEdit: () => void; }> = ({ technician, onEdit }) => {
  const { orders, deleteTechnician } = useApp();
  const [isExpanded, setIsExpanded] = useState(false);

  const completedRepairs = orders.filter(
    o => o.technicianId === technician.id && (o.status === OrderStatus.ReadyForDelivery || o.status === OrderStatus.Delivered)
  ).length;

  const activeJobs = orders.filter(o => 
    o.technicianId === technician.id && 
    ![OrderStatus.Delivered, OrderStatus.Cancelled].includes(o.status)
  );

  const roleColorMap: { [key: string]: string } = {
      [TechnicianRole.Owner]: 'bg-purple-100 text-purple-800',
      [TechnicianRole.Technician]: 'bg-blue-100 text-blue-800',
      [TechnicianRole.Cashier]: 'bg-green-100 text-green-800',
  };

  return (
    <Card className="mb-4 !p-0 overflow-hidden">
      <div className="p-4">
        <div className="flex items-center gap-4">
            {technician.avatarUrl ? (
                <img src={technician.avatarUrl} alt={technician.name} className="w-16 h-16 rounded-full object-cover" />
            ) : (
                <div className="w-16 h-16 rounded-full bg-accent-light flex items-center justify-center">
                    <User size={32} className="text-accent"/>
                </div>
            )}
          <div className="flex-grow">
            <div className="flex justify-between items-start">
              <div>
                <p className="font-bold text-lg text-gray-800">{technician.name}</p>
                <span className={`px-2.5 py-0.5 text-xs font-semibold rounded-full ${roleColorMap[technician.role]}`}>
                  {technician.role}
                </span>
              </div>
              {technician.role !== TechnicianRole.Owner && (
                  <div className="flex items-center">
                    <Button variant="ghost" size="sm" className="!text-gray-600 hover:!bg-gray-100 !p-2" onClick={onEdit}>
                        <Edit size={16} />
                    </Button>
                    <Button variant="ghost" size="sm" className="!text-red-600 hover:!bg-red-50 !p-2" onClick={() => deleteTechnician(technician.id)}>
                        <Trash2 size={16} />
                    </Button>
                  </div>
              )}
            </div>
            <div className="mt-2 flex items-center gap-2 text-gray-600">
               <UserCheck size={16} className="text-green-500" />
              <span className="text-sm font-medium">{completedRepairs} repairs completed</span>
            </div>
          </div>
        </div>
      </div>
       {/* Footer */}
      <div className="border-t border-gray-100 flex justify-between items-center px-4 py-2 bg-gray-50/70">
        <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex-grow text-left text-sm font-semibold text-gray-600 hover:text-accent transition-colors py-1 flex items-center gap-2"
            aria-expanded={isExpanded}
        >
            <span>Active Workload ({activeJobs.length})</span>
            <ChevronDown size={20} className={`transition-transform duration-300 ${isExpanded ? 'rotate-180' : ''}`} />
        </button>
        {technician.phone && (
            <div className="flex items-center gap-1 flex-shrink-0">
                <a href={`tel:${technician.phone}`} title="Call Technician" className="p-2 rounded-full text-green-600 hover:bg-green-100 transition-colors">
                    <Phone size={18} />
                </a>
                <a href={`https://wa.me/${technician.phone.replace(/\D/g, '')}`} title="WhatsApp Technician" target="_blank" rel="noopener noreferrer" className="p-2 rounded-full text-green-600 hover:bg-green-100 transition-colors">
                    <MessageCircle size={18} />
                </a>
            </div>
        )}
      </div>

       {isExpanded && (
        <div className="bg-gray-50 border-t border-gray-200 p-3">
          {activeJobs.length > 0 ? (
            <div>
                {activeJobs.map(job => <TechnicianJobItem key={job.id} job={job} />)}
            </div>
          ) : (
            <p className="text-center text-sm text-gray-500 py-4">No active jobs assigned.</p>
          )}
        </div>
      )}
    </Card>
  );
};

const resizeImage = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (event) => {
            const img = new Image();
            img.src = event.target?.result as string;
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const MAX_WIDTH = 256;
                const MAX_HEIGHT = 256;
                let { width, height } = img;

                if (width > height) {
                    if (width > MAX_WIDTH) {
                        height *= MAX_WIDTH / width;
                        width = MAX_WIDTH;
                    }
                } else {
                    if (height > MAX_HEIGHT) {
                        width *= MAX_HEIGHT / height;
                        height = MAX_HEIGHT;
                    }
                }
                canvas.width = width;
                canvas.height = height;
                const ctx = canvas.getContext('2d');
                if (!ctx) return reject(new Error('Could not get canvas context'));
                ctx.drawImage(img, 0, 0, width, height);
                resolve(canvas.toDataURL('image/jpeg', 0.9));
            };
            img.onerror = (error) => reject(error);
        };
        reader.onerror = (error) => reject(error);
    });
};

const defaultTechData = {
    name: '',
    phone: '',
    role: TechnicianRole.Technician,
    avatarUrl: '',
};

const TechniciansScreen: React.FC = () => {
  const { technicians, addTechnician, updateTechnician, setToast, filterQuery } = useApp();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingTechnician, setEditingTechnician] = useState<Technician | null>(null);
  
  const [newTechData, setNewTechData] = useState(defaultTechData);

  const [isActionSheetOpen, setActionSheetOpen] = useState(false);
  const [isCameraOpen, setCameraOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // This effect only populates the form when starting an edit.
  useEffect(() => {
    if (editingTechnician) {
        setNewTechData({
            name: editingTechnician.name,
            role: editingTechnician.role,
            avatarUrl: editingTechnician.avatarUrl || '',
            phone: editingTechnician.phone || '',
        });
    }
  }, [editingTechnician]);

  const handleOpenModal = (technician: Technician | null) => {
    // If we're adding a new technician, reset the form data first.
    if (!technician) {
        setNewTechData(defaultTechData);
    }
    setEditingTechnician(technician);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingTechnician(null);
    // Always reset form data on close to avoid stale state.
    setNewTechData(defaultTechData);
  };
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
      const { name, value } = e.target;
      setNewTechData(prev => ({ ...prev, [name]: value as TechnicianRole }));
  };
  
  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            try {
                const resizedImage = await resizeImage(file);
                setNewTechData(prev => ({ ...prev, avatarUrl: resizedImage }));
            } catch (error) {
                console.error("Error resizing image:", error);
                setToast("Could not process image.");
            }
        }
        event.target.value = '';
    };

  const handleCapture = (base64Image: string) => {
    setNewTechData(prev => ({ ...prev, avatarUrl: base64Image }));
    setCameraOpen(false);
  };


  const handleSaveTechnician = (e: React.FormEvent) => {
      e.preventDefault();
      if (!newTechData.name.trim()) {
          setToast("Technician name cannot be empty.");
          return;
      }
      
      if (editingTechnician) {
          updateTechnician({
              ...editingTechnician,
              ...newTechData
          });
          setToast("Technician updated successfully!");
      } else {
           addTechnician(newTechData);
           setToast("Technician added successfully!");
      }
      
      handleCloseModal();
  };
  
  const assignableRoles = Object.values(TechnicianRole).filter(role => role !== TechnicianRole.Owner);

  const filteredTechnicians = technicians.filter(tech => {
    if (!filterQuery) return true;
    return tech.name.toLowerCase().includes(filterQuery.toLowerCase());
  });

  return (
    <div className="space-y-6">
       <input type="file" accept="image/*" ref={fileInputRef} onChange={handleFileChange} className="hidden" />
       <ActionSheet isOpen={isActionSheetOpen} onClose={() => setActionSheetOpen(false)} title="Change Profile Photo">
            <Button variant="ghost" className="w-full !justify-start" onClick={() => {setActionSheetOpen(false); setCameraOpen(true)}}><CameraIcon size={20}/>Take Photo</Button>
            <Button variant="ghost" className="w-full !justify-start" onClick={() => {setActionSheetOpen(false); fileInputRef.current?.click()}}><ImageIcon size={20}/>Choose from Gallery</Button>
       </ActionSheet>
       <CameraModal isOpen={isCameraOpen} onClose={() => setCameraOpen(false)} onCapture={handleCapture} />

       <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-800">Technicians</h2>
        <Button size="sm" onClick={() => handleOpenModal(null)}>
          <PlusCircle size={16} />
          Add Technician
        </Button>
      </div>

      <Modal isOpen={isModalOpen} onClose={handleCloseModal} title={editingTechnician ? 'Edit Technician' : 'Add New Technician'}>
          <form onSubmit={handleSaveTechnician} className="space-y-4">
              <div className="flex flex-col items-center gap-4">
                <div className="relative">
                    {newTechData.avatarUrl ? (
                        <img src={newTechData.avatarUrl} alt="Avatar Preview" className="w-28 h-28 rounded-full object-cover ring-4 ring-gray-200"/>
                    ) : (
                        <div className="w-28 h-28 rounded-full bg-gray-200 flex items-center justify-center ring-4 ring-gray-200">
                             <User className="w-14 h-14 text-gray-400" />
                        </div>
                    )}
                    <button type="button" onClick={() => setActionSheetOpen(true)} className="absolute -bottom-1 -right-1 bg-accent text-white p-2 rounded-full shadow-md hover:bg-opacity-90 transition-all" aria-label="Upload photo">
                        <UploadCloud size={20} />
                    </button>
                    {newTechData.avatarUrl && (
                        <button type="button" onClick={() => setNewTechData(p => ({...p, avatarUrl: ''}))} className="absolute -bottom-1 -left-1 bg-red-500 text-white p-2 rounded-full shadow-md hover:bg-red-600 transition-all" aria-label="Remove photo">
                            <Trash2 size={20} />
                        </button>
                    )}
                </div>
              </div>
              <Input
                  label="Technician Name"
                  name="name"
                  value={newTechData.name}
                  onChange={handleInputChange}
                  icon={<User size={18} />}
                  placeholder="e.g., John Doe"
                  required
              />
              <Input
                  label="Phone Number (Optional)"
                  name="phone"
                  type="tel"
                  value={newTechData.phone}
                  onChange={handleInputChange}
                  icon={<Phone size={18} />}
                  placeholder="e.g., 9876543210"
              />
              <Select
                  label="Role"
                  name="role"
                  value={newTechData.role}
                  onChange={handleInputChange}
                  icon={<BriefcaseIcon size={18} />}
                  required
              >
                  {assignableRoles.map(role => (
                      <option key={role} value={role}>{role}</option>
                  ))}
              </Select>
              <div className="flex justify-end pt-4">
                  <Button type="submit">
                      <Save size={18} />
                      Save Technician
                  </Button>
              </div>
          </form>
      </Modal>

      <div>
        {filteredTechnicians.length > 0 ? (
          filteredTechnicians.map(tech => <TechnicianItem key={tech.id} technician={tech} onEdit={() => handleOpenModal(tech)} />)
        ) : (
          <Card className="text-center text-gray-500 py-8">
            {filterQuery ? `No technicians found for "${filterQuery}"` : 'No technicians found.'}
          </Card>
        )}
      </div>
    </div>
  );
};

export default TechniciansScreen;