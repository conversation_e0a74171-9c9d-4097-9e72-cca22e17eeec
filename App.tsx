
import React, { useEffect, useState } from 'react';
import { AppContextProvider, useApp } from './context/AppContext';
import TopBar from './components/layout/TopBar';
import BottomNav from './components/layout/BottomNav';
import DashboardScreen from './components/screens/DashboardScreen';
import OrdersScreen from './components/screens/OrdersScreen';
import InventoryScreen from './components/screens/InventoryScreen';
import WholesaleScreen from './components/screens/WholesaleScreen';
import CustomersScreen from './components/screens/CustomersScreen';
import ReportsScreen from './components/screens/ReportsScreen';
import SettingsScreen from './components/screens/SettingsScreen';
import TechniciansScreen from './components/screens/TechniciansScreen';
import ShopProfileScreen from './components/screens/ShopProfileScreen';
import SearchScreen from './components/screens/SearchScreen';
import NewOrderScreen from './components/screens/NewOrderScreen';
import OrderDetailsScreen from './components/screens/OrderDetailsScreen';
import EditOrderScreen from './components/screens/EditOrderScreen';
import CustomerProfileScreen from './components/screens/CustomerProfileScreen';
import BusinessHoursScreen from './components/screens/BusinessHoursScreen';
import Toast from './components/ui/Toast';
import Button from './components/ui/Button';
import PinLockScreen from './components/ui/PinLockScreen';
import PublicOrderViewScreen from './components/screens/PublicOrderViewScreen';

const App: React.FC = () => {
  return (
    <AppContextProvider>
      <AppContent />
    </AppContextProvider>
  );
};

const AppContent: React.FC = () => {
  const { 
    activeScreen, 
    setActiveScreen, 
    toast, 
    setToast,
    isLocked,
    appPin,
    unlockApp,
    setPinAndUnlock
  } = useApp();

  const [publicView, setPublicView] = useState<{isPublic: boolean, orderId: string | null}>({isPublic: false, orderId: null});

  useEffect(() => {
    const handleHashChange = () => {
        const hash = window.location.hash;
        const publicViewMatch = hash.match(/^#public\/view\/([^/]+)$/);

        if (publicViewMatch && publicViewMatch[1]) {
            setPublicView({ isPublic: true, orderId: publicViewMatch[1] });
        } else {
            // If hash is not a public view link, ensure we are not in public view mode.
            if(publicView.isPublic) {
              setPublicView({ isPublic: false, orderId: null });
            }
        }
    };

    window.addEventListener('hashchange', handleHashChange);
    handleHashChange(); // Check on initial load

    return () => {
        window.removeEventListener('hashchange', handleHashChange);
    };
  }, []);

  // Hide toast automatically after 3 seconds
  useEffect(() => {
    if (toast) {
      const timer = setTimeout(() => setToast(null), 3000);
      return () => clearTimeout(timer);
    }
  }, [toast, setToast]);
  
  if (publicView.isPublic && publicView.orderId) {
    return <PublicOrderViewScreen orderId={publicView.orderId} />;
  }
  
  const mainScreens = ['Home', 'Orders', 'Inventory', 'Wholesale', 'Technicians', 'Customers', 'Reports'];
  const settingSubScreens = ['ShopProfile', 'BusinessHours', 'DataSync', 'About'];

  const renderContent = () => {
    switch (activeScreen) {
      case 'Home':
        return <DashboardScreen />;
      case 'Orders':
        return <OrdersScreen />;
      case 'Inventory':
        return <InventoryScreen />;
      case 'Wholesale':
        return <WholesaleScreen />;
      case 'Technicians':
        return <TechniciansScreen />;
      case 'Customers':
        return <CustomersScreen />;
      case 'Reports':
        return <ReportsScreen />;
      case 'Settings':
        return <SettingsScreen />;
      case 'ShopProfile':
        return <ShopProfileScreen />;
      case 'Search':
        return <SearchScreen />;
      case 'NewOrder':
        return <NewOrderScreen />;
      case 'OrderDetails':
        return <OrderDetailsScreen />;
      case 'EditOrder':
        return <EditOrderScreen />;
      case 'CustomerProfile':
        return <CustomerProfileScreen />;
      case 'BusinessHours':
        return <BusinessHoursScreen />;
      case 'DataSync':
      case 'About':
         return (
          <div className="text-center p-8 space-y-4">
            <h2 className="text-2xl font-bold text-gray-700">Coming Soon!</h2>
            <p className="text-gray-500">This section is under active development. Check back later!</p>
            <Button onClick={() => setActiveScreen('Settings')}>Go Back</Button>
          </div>
        );
      default:
        return <DashboardScreen />;
    }
  };

  return (
    <>
      {isLocked && (
        <PinLockScreen 
          appPin={appPin}
          unlockApp={unlockApp}
          setPinAndUnlock={setPinAndUnlock}
        />
      )}
      <div className="min-h-screen font-sans flex flex-col max-w-lg mx-auto bg-white shadow-2xl">
        {toast && <Toast message={toast} />}
        <TopBar screen={activeScreen} />
        <main className="flex-grow p-4 pb-24">
          {renderContent()}
        </main>
        {(mainScreens.includes(activeScreen) || activeScreen === 'Settings') && !settingSubScreens.includes(activeScreen) && <BottomNav />}
      </div>
    </>
  );
}

export default App;
