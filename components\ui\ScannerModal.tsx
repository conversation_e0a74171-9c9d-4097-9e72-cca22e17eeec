import React, { useState, useRef, useEffect, useCallback } from 'react';
import { X, AlertTriangle } from 'lucide-react';
import Button from './Button';

interface ScannerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onScan: (scannedData: string) => void;
}

// BarcodeDetector might not be available on the window type
declare global {
  interface Window {
    BarcodeDetector: any;
  }
}

const ScannerModal: React.FC<ScannerModalProps> = ({ isOpen, onClose, onScan }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const animationFrameId = useRef<number | undefined>(undefined);
  const streamRef = useRef<MediaStream | null>(null);
  const [error, setError] = useState<string | null>(null);

  const stopScan = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    if (typeof animationFrameId.current === 'number') {
      cancelAnimationFrame(animationFrameId.current);
      animationFrameId.current = undefined;
    }
  }, []);

  useEffect(() => {
    if (!isOpen) {
      stopScan();
      return;
    }

    let isMounted = true;
    
    if (!('BarcodeDetector' in window)) {
      setError("QR Code scanning is not supported by your browser. Try a modern browser like Chrome.");
      return;
    }
    
    const barcodeDetector = new (window as any).BarcodeDetector({ formats: ['qr_code'] });

    const detectCode = async () => {
      if (videoRef.current && videoRef.current.readyState >= 2) {
        try {
          const barcodes = await barcodeDetector.detect(videoRef.current);
          if (isMounted && barcodes.length > 0) {
            onScan(barcodes[0].rawValue);
            onClose(); // This will trigger cleanup
            return;
          }
        } catch (e) {
          console.error('Barcode detection error:', e);
        }
      }
      if (isMounted) {
        animationFrameId.current = requestAnimationFrame(detectCode);
      }
    };
    
    const startScan = async () => {
      setError(null);
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ video: { facingMode: 'environment' } });
        if (!isMounted) {
            stream.getTracks().forEach(track => track.stop());
            return;
        }

        streamRef.current = stream;
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
          videoRef.current.play().then(() => {
            if (isMounted) {
                animationFrameId.current = requestAnimationFrame(detectCode);
            }
          }).catch(err => {
            console.error("Video play failed:", err);
            if (isMounted) setError("Could not start video. Please check permissions.");
          });
        }
      } catch (err: any) {
        console.error("Camera access error:", err);
        if(isMounted) {
            if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
              setError('Camera permission denied. Please enable it in your browser settings.');
            } else {
              setError('Could not access camera. Is it being used by another app?');
            }
        }
      }
    };

    startScan();

    return () => {
      isMounted = false;
      stopScan();
    };
  }, [isOpen, onClose, onScan, stopScan]);

  if (!isOpen) return null;

  return (
    <div role="dialog" aria-modal="true" className="fixed inset-0 z-[70] bg-black flex flex-col items-center justify-center">
      <div className="relative w-full h-full">
        <video ref={videoRef} playsInline autoPlay className="w-full h-full object-cover" />
        
        {/* Viewfinder and animated scan line */}
        <div className="absolute inset-0 flex items-center justify-center">
             <div className="relative w-[70vw] h-[70vw] max-w-[300px] max-h-[300px] border-4 border-white/50 rounded-lg overflow-hidden">
                <div className="absolute top-0 left-0 w-full h-1 bg-accent animate-scan-line"></div>
             </div>
        </div>

        {error && (
            <div className="absolute inset-0 bg-black/80 flex flex-col items-center justify-center text-center p-4">
                <AlertTriangle className="text-yellow-400 w-16 h-16 mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">Scanner Error</h3>
                <p className="text-gray-300">{error}</p>
            </div>
        )}
      </div>

      <div className="absolute top-4 right-4">
        <Button onClick={onClose} variant="secondary" size="sm" className="!rounded-full !p-2 !bg-black/50 hover:!bg-black/70 !text-white">
            <X size={24} />
        </Button>
      </div>
      
      <style>{`
        @keyframes scan-line-anim {
          0% { transform: translateY(0); }
          100% { transform: translateY(calc(100% - 4px)); }
        }
        .animate-scan-line {
            animation: scan-line-anim 2.5s infinite alternate ease-in-out;
            box-shadow: 0px 0px 10px 2px var(--color-accent);
            height: 4px;
        }
      `}</style>
    </div>
  );
};

export default ScannerModal;