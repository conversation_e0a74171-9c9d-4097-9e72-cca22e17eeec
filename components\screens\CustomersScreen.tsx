

import React, { useState, useRef } from 'react';
import { useApp } from '../../context/AppContext';
import type { Customer } from '../../types';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { PlusCircle, User, Save, ChevronRight, Briefcase, Phone, MessageCircle } from 'lucide-react';
import Modal from '../ui/Modal';
import Input from '../ui/Input';
import useAutocomplete from '../../hooks/useAutocomplete';

const CustomerItem: React.FC<{ customer: Customer }> = ({ customer }) => {
  const { orders, viewCustomerDetails } = useApp();
  const repairHistoryCount = orders.filter(o => o.customer.id === customer.id).length;
  
  return (
    <Card className="mb-4 !p-0 overflow-hidden">
        {/* Main content, clickable */}
        <div className="p-4 hover:bg-gray-50 transition-colors cursor-pointer" onClick={() => viewCustomerDetails(customer.id)}>
          <div className="flex justify-between items-center">
              <div className="flex items-center gap-4 min-w-0">
                  <div className="w-12 h-12 rounded-full bg-accent-light flex items-center justify-center flex-shrink-0 overflow-hidden">
                      {customer.avatarUrl ? (
                          <img src={customer.avatarUrl} alt={customer.name} className="w-full h-full object-cover" />
                      ) : (
                          <User size={24} className="text-accent" />
                      )}
                  </div>
                  <div className="min-w-0">
                    <p className="font-bold text-gray-800 truncate">{customer.name}</p>
                    <p className="text-sm text-gray-500 truncate">{customer.phone}</p>
                  </div>
              </div>
              <ChevronRight size={20} className="text-gray-400 flex-shrink-0 ml-2" />
          </div>
        </div>

        {/* Footer with actions */}
        <div className="bg-gray-50/70 px-4 py-3 flex justify-between items-center border-t border-gray-100">
            <div className="flex items-center gap-2 text-sm text-gray-600">
                <Briefcase size={14} className="text-accent"/>
                <span>{repairHistoryCount} Repair{repairHistoryCount !== 1 ? 's' : ''} on record</span>
            </div>
            <div className="flex items-center gap-2">
                <a href={`tel:${customer.phone}`} title="Call Customer" className="p-2 rounded-full text-green-600 hover:bg-green-100 transition-colors">
                    <Phone size={18} />
                </a>
                <a href={`https://wa.me/${customer.phone.replace(/\D/g, '')}`} title="WhatsApp Customer" target="_blank" rel="noopener noreferrer" className="p-2 rounded-full text-green-600 hover:bg-green-100 transition-colors">
                    <MessageCircle size={18} />
                </a>
            </div>
        </div>
    </Card>
  );
};

const CustomersScreen: React.FC = () => {
  const { customers, addCustomer, setToast, filterQuery } = useApp();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [newCustomerData, setNewCustomerData] = useState({
      name: '',
      phone: '',
      address: '',
  });
  
  const addressInputRef = useRef<HTMLInputElement>(null);

  const handlePlaceSelected = (place: any) => {
    if (place?.formatted_address) {
      setNewCustomerData(prev => ({ ...prev, address: place.formatted_address }));
    }
  };

  useAutocomplete(addressInputRef, handlePlaceSelected);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setNewCustomerData(prev => ({ ...prev, [name]: value }));
  };
  
  const handleAddCustomer = (e: React.FormEvent) => {
      e.preventDefault();
      if (!newCustomerData.name.trim() || !newCustomerData.phone.trim()) {
          setToast("Customer name and phone cannot be empty.");
          return;
      }
      addCustomer(newCustomerData);
      setToast("Customer added successfully!");
      setIsAddModalOpen(false);
      setNewCustomerData({ name: '', phone: '', address: '' });
  };
  
  const filteredCustomers = customers.filter(customer => {
    if (!filterQuery) return true;
    const lowerQuery = filterQuery.toLowerCase();
    return (
        customer.name.toLowerCase().includes(lowerQuery) ||
        customer.phone.toLowerCase().includes(lowerQuery)
    );
  });

  return (
    <div className="space-y-6">
       <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-800">Customers</h2>
        <Button size="sm" onClick={() => setIsAddModalOpen(true)}>
          <PlusCircle size={16} />
          New Customer
        </Button>
      </div>

      <Modal isOpen={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} title="Add New Customer">
          <form onSubmit={handleAddCustomer} className="space-y-4">
              <Input
                  label="Customer Name"
                  name="name"
                  value={newCustomerData.name}
                  onChange={handleInputChange}
                  icon={<User size={18} />}
                  placeholder="e.g., Jane Smith"
                  required
              />
              <Input
                  label="Phone Number"
                  name="phone"
                  type="tel"
                  value={newCustomerData.phone}
                  onChange={handleInputChange}
                  icon={<Briefcase size={18} />}
                  placeholder="10-digit mobile number"
                  required
              />
              <Input
                  ref={addressInputRef}
                  label="Address (autocomplete)"
                  name="address"
                  value={newCustomerData.address}
                  onChange={handleInputChange}
                  icon={<Briefcase size={18} />}
                  placeholder="Start typing address..."
              />
              <div className="flex justify-end pt-4">
                  <Button type="submit">
                      <Save size={18} />
                      Save Customer
                  </Button>
              </div>
          </form>
      </Modal>

      <div>
        {filteredCustomers.length > 0 ? (
          filteredCustomers.map(customer => <CustomerItem key={customer.id} customer={customer} />)
        ) : (
           <Card className="text-center text-gray-500 py-8">
            {filterQuery ? `No customers found for "${filterQuery}"` : 'No customers found.'}
           </Card>
        )}
      </div>
    </div>
  );
};

export default CustomersScreen;