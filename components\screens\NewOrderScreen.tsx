
import React, { useState, useRef, ChangeEvent } from 'react';
import { useApp } from '../../context/AppContext';
import { JobPriority, UsedPart, InventoryItem } from '../../types';
import Card from '../ui/Card';
import Button from '../ui/Button';
import Input from '../ui/Input';
import TextArea from '../ui/TextArea';
import Select from '../ui/Select';
import ActionSheet from '../ui/ActionSheet';
import CameraModal from '../ui/CameraModal';
import Modal from '../ui/Modal';
import { User, Phone, MapPin, Smartphone, Hash, FileText, Camera, Trash2, Tag, DollarSign, Briefcase, Edit, Send, PlusCircle, Package, Search } from 'lucide-react';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

interface JobFormState {
    customerName: string;
    customerPhone: string;
    customerAddress: string;
    brand: string;
    deviceModel: string;
    imei: string;
    serialNo: string;
    issueDescription: string;
    priority: JobPriority;
    photoUrl: string;
    estimatedCost: string;
    advancePayment: string;
    technicianId: string;
    usedParts: UsedPart[];
    laborCost: string;
}

const resizeImage = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (event) => {
            const img = new Image();
            img.src = event.target?.result as string;
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const MAX_WIDTH = 800;
                const MAX_HEIGHT = 800;
                let { width, height } = img;

                if (width > height) {
                    if (width > MAX_WIDTH) {
                        height *= MAX_WIDTH / width;
                        width = MAX_WIDTH;
                    }
                } else {
                    if (height > MAX_HEIGHT) {
                        width *= MAX_HEIGHT / height;
                        height = MAX_HEIGHT;
                    }
                }
                canvas.width = width;
                canvas.height = height;
                const ctx = canvas.getContext('2d');
                if (!ctx) {
                    return reject(new Error('Could not get canvas context'));
                }
                ctx.drawImage(img, 0, 0, width, height);
                resolve(canvas.toDataURL('image/jpeg', 0.85));
            };
            img.onerror = (error) => reject(error);
        };
        reader.onerror = (error) => reject(error);
    });
};


const NewOrderScreen: React.FC = () => {
    const { technicians, inventory, createJobCard, setToast, shopProfile, setActiveScreen } = useApp();
    const [formData, setFormData] = useState<JobFormState>({
        customerName: '',
        customerPhone: '',
        customerAddress: '',
        brand: '',
        deviceModel: '',
        imei: '',
        serialNo: '',
        issueDescription: '',
        priority: JobPriority.Normal,
        photoUrl: '',
        estimatedCost: '',
        advancePayment: '0',
        technicianId: '',
        usedParts: [],
        laborCost: '',
    });

    const [isActionSheetOpen, setActionSheetOpen] = useState(false);
    const [isCameraOpen, setCameraOpen] = useState(false);
    const [isPreviewing, setIsPreviewing] = useState(false);
    const [generatedOrderId, setGeneratedOrderId] = useState('');
    const [isPartsModalOpen, setIsPartsModalOpen] = useState(false);
    const [partSearch, setPartSearch] = useState('');
    const fileInputRef = useRef<HTMLInputElement>(null);
    const previewRef = useRef<HTMLDivElement>(null);
    
    const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({...prev, [name]: value}));
    };
    
    const handleFileChange = async (event: ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            try {
                const resizedImage = await resizeImage(file);
                setFormData(prev => ({ ...prev, photoUrl: resizedImage }));
            } catch (error) {
                console.error("Error resizing image:", error);
                setToast("Could not process image.");
            }
        }
        event.target.value = '';
    };
    
    const handleCapture = (base64Image: string) => {
        setFormData(prev => ({ ...prev, photoUrl: base64Image }));
        setCameraOpen(false);
    };

    const handlePreview = (e: React.FormEvent) => {
        e.preventDefault();
        if (!formData.customerName || !formData.customerPhone || !formData.deviceModel || !formData.issueDescription || !formData.estimatedCost) {
            setToast('Please fill all required fields, including cost.');
            return;
        }
        setGeneratedOrderId(`ORD-${Date.now()}`);
        setIsPreviewing(true);
    };

    const handleConfirmAndSend = async () => {
        // 1. Save the order
        const newOrder = createJobCard({
            ...formData,
            id: generatedOrderId,
            estimatedCost: parseFloat(formData.estimatedCost) || 0,
            advancePayment: parseFloat(formData.advancePayment) || 0,
            laborCost: parseFloat(formData.laborCost) || 0,
        });

        if (!newOrder) {
            setToast("Failed to create order. Please try again.");
            return;
        }

        // 2. Generate and download PDF
        if (previewRef.current) {
            try {
                const canvas = await html2canvas(previewRef.current, { scale: 2 });
                const imgData = canvas.toDataURL('image/png');
                const pdf = new jsPDF('p', 'mm', 'a4');
                const pdfWidth = pdf.internal.pageSize.getWidth();
                const pdfHeight = (canvas.height * pdfWidth) / canvas.width;
                pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);
                pdf.save(`JobCard-${newOrder.id}.pdf`);
            } catch(e) {
                console.error("Error generating PDF", e);
                setToast("Could not generate PDF.");
            }
        }
        
        // 3. Prepare and open WhatsApp
        const estimatedCostNum = parseFloat(formData.estimatedCost) || 0;
        const advancePaymentNum = parseFloat(formData.advancePayment) || 0;
        const amountDue = estimatedCostNum - advancePaymentNum;
        
        const publicUrl = window.location.href.split('#')[0] + '#public/view/' + newOrder.id;

        const message = `Hello ${formData.customerName},

Thank you for choosing *${shopProfile.name}*!

Here is the summary of your service request:
*Order ID:* ${newOrder.id}
*Device:* ${formData.brand} ${formData.deviceModel}
*Problem:* ${formData.issueDescription}

*Financials:*
- Estimated Cost: ₹${estimatedCostNum.toLocaleString('en-IN')}
- Advance Paid: ₹${advancePaymentNum.toLocaleString('en-IN')}
- Amount Due: ₹${amountDue.toLocaleString('en-IN')}

You can track the status of your repair live here:
${publicUrl}

We will keep you updated on the progress.

*${shopProfile.name}*
${shopProfile.address || ''}
${shopProfile.phone || ''}`;

        const whatsappUrl = `https://wa.me/${formData.customerPhone}?text=${encodeURIComponent(message)}`;
        window.open(whatsappUrl, '_blank');
        
        // 4. Navigate back to orders screen
        setActiveScreen('Orders');
    };

    const handleAddPart = (item: InventoryItem, quantity: number) => {
        if (quantity <= 0) {
            setToast('Quantity must be positive.');
            return;
        }
        const existingPart = formData.usedParts.find(p => p.inventoryItemId === item.id);
        const currentUsedQuantity = existingPart?.quantity || 0;
        if (quantity + currentUsedQuantity > item.quantity) {
            setToast(`Cannot use more than available in stock (${item.quantity}).`);
            return;
        }
        let newParts: UsedPart[];
        if (existingPart) {
            newParts = formData.usedParts.map(p => p.inventoryItemId === item.id ? { ...p, quantity: p.quantity + quantity } : p);
        } else {
            newParts = [...formData.usedParts, { inventoryItemId: item.id, name: item.name, quantity, sellingPrice: item.sellingPrice }];
        }
        setFormData(prev => ({ ...prev, usedParts: newParts }));
        setToast(`${item.name} (x${quantity}) added.`);
    };

    const handleRemovePart = (inventoryItemId: string) => {
        setFormData(prev => ({ ...prev, usedParts: prev.usedParts.filter(p => p.inventoryItemId !== inventoryItemId) }));
    };
    
    const partsTotal = formData.usedParts.reduce((sum, part) => sum + part.sellingPrice * part.quantity, 0);
    const laborCost = parseFloat(formData.laborCost) || 0;
    const calculatedTotal = partsTotal + laborCost;
    const filteredInventory = inventory.filter(item => item.name.toLowerCase().includes(partSearch.toLowerCase()));

    if (isPreviewing) {
        const estimatedCostNum = parseFloat(formData.estimatedCost) || 0;
        const advancePaymentNum = parseFloat(formData.advancePayment) || 0;

        return (
            <div className="bg-white absolute inset-0 z-20 p-4 overflow-y-auto">
                <div ref={previewRef} className="bg-white p-6 rounded-lg shadow-lg max-w-2xl mx-auto">
                    {/* Header */}
                    <div className="flex justify-between items-start pb-4 border-b">
                        <div className="flex items-center gap-4">
                            {shopProfile.avatarUrl && <img src={shopProfile.avatarUrl} alt="logo" className="w-16 h-16 rounded-full object-cover"/>}
                            <div>
                                <h1 className="text-2xl font-bold text-gray-800">{shopProfile.name}</h1>
                                <p className="text-sm text-gray-500">{shopProfile.address}</p>
                                <p className="text-sm text-gray-500">{shopProfile.phone}</p>
                            </div>
                        </div>
                        <div className="text-right">
                            <h2 className="text-xl font-semibold text-gray-600">JOB CARD</h2>
                            <p className="text-sm text-gray-500">ID: {generatedOrderId}</p>
                            <p className="text-sm text-gray-500">Date: {new Date().toLocaleDateString()}</p>
                        </div>
                    </div>
                    {/* Customer & Device Details */}
                    <div className="grid grid-cols-2 gap-6 my-4">
                        <div>
                            <h3 className="font-bold text-gray-700 mb-2 border-b pb-1">CUSTOMER DETAILS</h3>
                            <p className="font-semibold text-gray-800">{formData.customerName}</p>
                            <p className="text-sm text-gray-600">{formData.customerPhone}</p>
                            <p className="text-sm text-gray-600">{formData.customerAddress}</p>
                        </div>
                        <div>
                            <h3 className="font-bold text-gray-700 mb-2 border-b pb-1">DEVICE DETAILS</h3>
                            <p className="font-semibold text-gray-800">{formData.brand} {formData.deviceModel}</p>
                            {formData.imei && <p className="text-sm text-gray-600">IMEI: {formData.imei}</p>}
                            {formData.serialNo && <p className="text-sm text-gray-600">S/N: {formData.serialNo}</p>}
                        </div>
                    </div>
                    {/* Issue Description */}
                    <div>
                        <h3 className="font-bold text-gray-700 mb-2 border-b pb-1">REPORTED ISSUE</h3>
                        <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">{formData.issueDescription}</p>
                    </div>

                     {/* Financials */}
                    <div className="my-4">
                        <h3 className="font-bold text-gray-700 mb-2 border-b pb-1">FINANCIALS</h3>
                        {formData.usedParts.length > 0 && <div className="text-sm my-1"><span className="text-gray-600">Parts Cost:</span><span className="font-medium text-gray-800 float-right">₹{partsTotal.toLocaleString('en-IN')}</span></div>}
                        {laborCost > 0 && <div className="text-sm my-1"><span className="text-gray-600">Labor Cost:</span><span className="font-medium text-gray-800 float-right">₹{laborCost.toLocaleString('en-IN')}</span></div>}
                        <div className="flex justify-between items-center font-bold text-base mt-2 pt-2 border-t">
                            <span className="text-gray-800">Total Estimated Cost:</span>
                            <span className="text-accent">₹{estimatedCostNum.toLocaleString('en-IN')}</span>
                        </div>
                         <div className="flex justify-between items-center text-sm my-1">
                            <span className="text-gray-600">Advance Payment:</span>
                            <span className="font-semibold text-gray-800">₹{advancePaymentNum.toLocaleString('en-IN')}</span>
                        </div>
                        <div className="flex justify-between items-center font-bold text-base mt-2 pt-2 border-t">
                            <span className="text-gray-800">Amount Due:</span>
                            <span className="text-accent">₹{(estimatedCostNum - advancePaymentNum).toLocaleString('en-IN')}</span>
                        </div>
                    </div>
                    
                    {/* Terms & Signature */}
                    <div className="mt-6 pt-4 border-t text-xs text-gray-500">
                        <h4 className="font-bold mb-1">Terms & Conditions</h4>
                        <p>1. The estimated cost may change after diagnosis. Customer will be notified before proceeding.</p>
                        <p>2. The shop is not responsible for data loss. Please backup your device.</p>
                        <p>3. Devices not collected within 30 days of notification may be disposed of.</p>
                        <div className="mt-8 pt-8 border-t border-dashed">
                             <p>Customer Signature</p>
                        </div>
                    </div>
                </div>

                <div className="py-6 max-w-2xl mx-auto flex gap-3">
                    <Button variant="secondary" onClick={() => setIsPreviewing(false)} className="w-full">
                        <Edit size={16}/>
                        Edit
                    </Button>
                    <Button onClick={handleConfirmAndSend} className="w-full">
                        <Send size={16}/>
                        Confirm & Send to Customer
                    </Button>
                </div>
            </div>
        );
    }


    return (
        <div className="space-y-6">
            <input type="file" accept="image/*" ref={fileInputRef} onChange={handleFileChange} className="hidden" />
            <ActionSheet isOpen={isActionSheetOpen} onClose={() => setActionSheetOpen(false)} title="Add Device Photo">
                <Button variant="ghost" className="w-full !justify-start" onClick={() => {setActionSheetOpen(false); setCameraOpen(true)}}><Camera size={20}/>Take Photo</Button>
                <Button variant="ghost" className="w-full !justify-start" onClick={() => {setActionSheetOpen(false); fileInputRef.current?.click()}}><FileText size={20}/>Choose from Gallery</Button>
            </ActionSheet>
            <CameraModal isOpen={isCameraOpen} onClose={() => setCameraOpen(false)} onCapture={handleCapture} />
            <Modal isOpen={isPartsModalOpen} onClose={() => setIsPartsModalOpen(false)} title="Add Part to Job" size="lg">
                <div className="space-y-4">
                    <Input icon={<Search size={18} />} label="Search Parts" placeholder="e.g., Screen, Battery..." value={partSearch} onChange={e => setPartSearch(e.target.value)} />
                    <div className="max-h-96 overflow-y-auto space-y-2 pr-2">
                        {filteredInventory.length > 0 ? filteredInventory.map(item => {
                            const partInOrder = formData.usedParts.find(p => p.inventoryItemId === item.id);
                            const availableStock = item.quantity - (partInOrder?.quantity || 0);
                            return (
                                <div key={item.id} className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                                    <div className="flex-grow">
                                        <p className="font-semibold text-gray-800">{item.name}</p>
                                        <p className="text-xs text-gray-500">In Stock: {item.quantity} | Available: {availableStock}</p>
                                    </div>
                                    <Button size="sm" onClick={() => handleAddPart(item, 1)} disabled={availableStock <= 0}>
                                        <PlusCircle size={16}/> Add
                                    </Button>
                                </div>
                            );
                        }) : <p className="text-center text-gray-500 p-4">No parts found.</p>}
                    </div>
                </div>
            </Modal>

            <form onSubmit={handlePreview} className="space-y-6">
                <Card>
                    <h3 className="text-lg font-bold text-gray-700 mb-4">Customer Details</h3>
                    <div className="space-y-4">
                        <Input required name="customerName" label="Full Name" value={formData.customerName} onChange={handleChange} icon={<User size={18} />} />
                        <Input required name="customerPhone" label="Phone Number" type="tel" value={formData.customerPhone} onChange={handleChange} icon={<Phone size={18} />} />
                        <Input name="customerAddress" label="Address" placeholder="Customer's full address" value={formData.customerAddress} onChange={handleChange} icon={<MapPin size={18} />} />
                    </div>
                </Card>

                <Card>
                    <h3 className="text-lg font-bold text-gray-700 mb-4">Device Details</h3>
                    <div className="space-y-4">
                        <Input name="brand" label="Brand (e.g., Apple, Samsung)" value={formData.brand} onChange={handleChange} icon={<Tag size={18} />} />
                        <Input required name="deviceModel" label="Model (e.g., iPhone 13 Pro)" value={formData.deviceModel} onChange={handleChange} icon={<Smartphone size={18} />} />
                        <Input name="imei" label="IMEI Number" value={formData.imei} onChange={handleChange} icon={<Hash size={18} />} />
                        <Input name="serialNo" label="Serial Number" value={formData.serialNo} onChange={handleChange} icon={<Hash size={18} />} />
                    </div>
                </Card>

                <Card>
                    <h3 className="text-lg font-bold text-gray-700 mb-4">Job Details</h3>
                    <div className="space-y-4">
                        <TextArea required name="issueDescription" label="Problem Description" rows={4} value={formData.issueDescription} onChange={handleChange} />
                        <div>
                             <label className="block text-sm font-medium text-gray-700 mb-2">Condition Photo</label>
                             {formData.photoUrl ? (
                                <div className="relative group">
                                    <img src={formData.photoUrl} alt="Device condition" className="w-full h-48 object-cover rounded-lg" />
                                    <button type="button" onClick={() => setFormData(p => ({...p, photoUrl: ''}))} className="absolute top-2 right-2 bg-red-500 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity">
                                        <Trash2 size={18} />
                                    </button>
                                </div>
                             ) : (
                                <Button type="button" variant="secondary" onClick={() => setActionSheetOpen(true)}>
                                    <Camera size={16} /> Add Photo
                                </Button>
                             )}
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                            <div className="flex gap-2">
                                {(Object.keys(JobPriority) as Array<keyof typeof JobPriority>).map(key => (
                                    <button type="button" key={key} onClick={() => setFormData(p => ({ ...p, priority: JobPriority[key]}))} 
                                    className={`flex-1 text-center p-2 rounded-lg border-2 transition-all ${formData.priority === JobPriority[key] ? 'border-accent bg-accent-light' : 'border-gray-200 bg-white hover:border-gray-300'}`}>
                                        <span className={`font-semibold ${formData.priority === JobPriority[key] ? 'text-accent' : 'text-gray-600'}`}>{JobPriority[key]}</span>
                                    </button>
                                ))}
                            </div>
                        </div>
                    </div>
                </Card>
                
                <Card>
                    <h3 className="text-lg font-bold text-gray-700 mb-4">Parts, Financials & Assignment</h3>
                     <div className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Parts Used</label>
                            {formData.usedParts.length > 0 ? (
                                <div className="space-y-2">
                                    {formData.usedParts.map(part => (
                                        <div key={part.inventoryItemId} className="flex items-center gap-2 bg-gray-50 p-2 rounded-md">
                                            <Package size={16} className="text-gray-500"/>
                                            <div className="flex-grow"><p className="font-medium text-sm text-gray-800">{part.name}</p><p className="text-xs text-gray-500">{part.quantity} x ₹{part.sellingPrice}</p></div>
                                            <p className="font-semibold text-sm">₹{part.quantity * part.sellingPrice}</p>
                                            <button type="button" onClick={() => handleRemovePart(part.inventoryItemId)} className="p-1 text-red-500 hover:bg-red-100 rounded-full"><Trash2 size={16}/></button>
                                        </div>
                                    ))}
                                </div>
                            ) : (<p className="text-sm text-gray-500 text-center py-2 bg-gray-50 rounded-md">No parts added yet.</p>)}
                            <Button type="button" variant="secondary" size="sm" className="mt-2 w-full" onClick={() => setIsPartsModalOpen(true)}><PlusCircle size={16} /> Add Part</Button>
                        </div>
                        <Input name="laborCost" label="Labor & Service Cost (₹)" type="number" value={formData.laborCost} onChange={handleChange} icon={<DollarSign size={18} />} placeholder="0" />
                        <div className="p-3 bg-accent-light rounded-lg text-center">
                            <p className="text-sm text-accent font-semibold">Calculated Total (Parts + Labor)</p>
                            <p className="text-2xl font-bold text-accent">₹{calculatedTotal.toLocaleString('en-IN')}</p>
                            <p className="text-xs text-gray-500">Use this to set the final estimated cost below.</p>
                        </div>
                        <Input required name="estimatedCost" label="Final Estimated Cost (₹)" type="number" value={formData.estimatedCost} onChange={handleChange} icon={<DollarSign size={18} />} />
                        <Input name="advancePayment" label="Advance Payment (₹)" type="number" value={formData.advancePayment} onChange={handleChange} icon={<DollarSign size={18} />} />
                        <Select name="technicianId" label="Assign Technician" value={formData.technicianId} onChange={handleChange} icon={<Briefcase size={18} />}>
                            <option value="">Assign later</option>
                            {technicians.map(tech => <option key={tech.id} value={tech.id}>{tech.name}</option>)}
                        </Select>
                     </div>
                </Card>

                <div className="pb-4">
                    <Button type="submit" size="lg" className="w-full">Preview Job Card</Button>
                </div>
            </form>
        </div>
    );
};

export default NewOrderScreen;
