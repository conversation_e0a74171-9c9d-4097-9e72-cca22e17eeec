

import React from 'react';
import { Home, Wrench, Package, Users, BarChart, Briefcase, ShoppingBag } from 'lucide-react';
import { useApp } from '../../context/AppContext';

type Tab = 'Home' | 'Orders' | 'Inventory' | 'Wholesale' | 'Technicians' | 'Customers' | 'Reports' | 'Settings';

const navItems = [
  { name: 'Home', icon: Home },
  { name: 'Orders', icon: Wrench },
  { name: 'Inventory', icon: Package },
  { name: 'Wholesale', icon: ShoppingBag },
  { name: 'Technicians', icon: Briefcase },
  { name: 'Customers', icon: Users },
  { name: 'Reports', icon: Bar<PERSON><PERSON> },
];

const NavItem: React.FC<{
  item: { name: string; icon: React.ElementType };
  isActive: boolean;
  onClick: () => void;
}> = ({ item, isActive, onClick }) => {
  const Icon = item.icon;
  const color = isActive ? 'text-accent' : 'text-gray-500';

  return (
    <button onClick={onClick} className={`flex flex-1 flex-col items-center justify-center gap-1 transition-colors duration-200 ${color} hover:text-accent p-1`}>
      <Icon size={24} />
      <span className="text-xs font-medium text-center">{item.name}</span>
    </button>
  );
};

const BottomNav: React.FC = () => {
    const { activeScreen, setActiveScreen } = useApp();
  return (
    <nav className="fixed bottom-0 left-0 right-0 max-w-lg mx-auto bg-white shadow-[0_-2px_10px_rgba(0,0,0,0.05)] border-t border-gray-200">
      <div className="flex justify-around items-stretch h-16 px-1">
        {navItems.map((item) => (
          <NavItem
            key={item.name}
            item={item}
            isActive={activeScreen === item.name}
            onClick={() => setActiveScreen(item.name as Tab)}
          />
        ))}
      </div>
    </nav>
  );
};

export default BottomNav;