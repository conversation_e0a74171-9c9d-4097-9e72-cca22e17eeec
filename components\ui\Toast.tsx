
import React from 'react';
import { CheckCircle } from 'lucide-react';

interface ToastProps {
  message: string;
}

const Toast: React.FC<ToastProps> = ({ message }) => {
  return (
    <div 
      role="status"
      aria-live="polite"
      className="fixed top-5 left-1/2 -translate-x-1/2 z-50 bg-gray-800 text-white py-2.5 px-5 rounded-full shadow-lg flex items-center gap-2 animate-fade-in-down"
    >
      <CheckCircle size={18} className="text-green-400" />
      <span className="text-sm font-medium">{message}</span>
    </div>
  );
};

export default Toast;
