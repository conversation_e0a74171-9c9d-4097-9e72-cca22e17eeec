import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:io';

import '../app_state.dart';
import '../models.dart';
import '../theme/facebook_theme.dart';
import 'order_details.dart';
import 'new_order.dart';

class OrdersScreen extends StatefulWidget {
  const OrdersScreen({super.key});

  @override
  State<OrdersScreen> createState() => _OrdersScreenState();
}

class _OrdersScreenState extends State<OrdersScreen> {
  String filter = 'Active';
  final filters = const ['Active', 'Ready', 'Completed', 'Cancelled', 'All'];
  final searchController = TextEditingController();
  String searchQuery = '';

  bool _matchesFilter(Order o) {
    // First apply status filter
    bool statusMatch;
    switch (filter) {
      case 'Active':
        statusMatch = !{
          OrderStatus.delivered,
          OrderStatus.cancelled,
          OrderStatus.readyForDelivery,
        }.contains(o.status);
        break;
      case 'Ready':
        statusMatch = o.status == OrderStatus.readyForDelivery;
        break;
      case 'Completed':
        statusMatch = o.status == OrderStatus.delivered;
        break;
      case 'Cancelled':
        statusMatch = o.status == OrderStatus.cancelled;
        break;
      case 'All':
      default:
        statusMatch = true;
    }
    
    // Then apply search filter
    if (!statusMatch) return false;
    if (searchQuery.isEmpty) return true;
    
    final query = searchQuery.toLowerCase();
    return o.customer.name.toLowerCase().contains(query) ||
           o.customer.phone.contains(query) ||
           o.deviceModel.toLowerCase().contains(query) ||
           o.id.toLowerCase().contains(query) ||
           (o.brand?.toLowerCase().contains(query) ?? false);
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.black, Color(0xFF0A0A0A)],
        ),
      ),
      child: Consumer<AppState>(builder: (_, state, __) {
        final list = state.orders.where(_matchesFilter).toList();
        return Column(children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 20, 20, 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: FacebookTheme.facebookBlue,
                        borderRadius: FacebookTheme.facebookRadius,
                        boxShadow: FacebookTheme.facebookShadow,
                      ),
                      child: const Icon(Icons.build_rounded, color: Colors.white, size: 24),
                    ),
                    const SizedBox(width: 16),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Repair Orders',
                          style: FacebookTheme.facebookHeading2.copyWith(
                            color: Theme.of(context).brightness == Brightness.dark
                              ? FacebookTheme.darkText
                              : FacebookTheme.lightText
                          )),
                        Text('Manage device repairs',
                          style: FacebookTheme.facebookBody2.copyWith(
                            color: Theme.of(context).brightness == Brightness.dark
                              ? FacebookTheme.darkSecondaryText
                              : FacebookTheme.lightSecondaryText
                          )),
                      ],
                    ),
                  ],
                ),
                FacebookWidgets.facebookButton(
                  text: 'New Repair',
                  onPressed: () => Navigator.of(context).pushNamed('/new'),
                  isPrimary: true,
                  isDark: Theme.of(context).brightness == Brightness.dark,
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Container(
              decoration: BoxDecoration(
                color: const Color(0xFF1F1F1F),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.white12),
              ),
              child: TextField(
                controller: searchController,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'Search orders...',
                  hintStyle: const TextStyle(color: Colors.white54),
                  prefixIcon: const Icon(Icons.search, color: Colors.white70),
                  suffixIcon: searchQuery.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear, color: Colors.white70),
                          onPressed: () {
                            searchController.clear();
                            setState(() => searchQuery = '');
                          },
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(16),
                ),
                onChanged: (value) => setState(() => searchQuery = value),
              ),
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 50,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              itemBuilder: (_, i) {
                final f = filters[i];
                final selected = f == filter;
                return Container(
                  decoration: BoxDecoration(
                    color: selected
                      ? FacebookTheme.facebookBlue
                      : (Theme.of(context).brightness == Brightness.dark
                          ? FacebookTheme.darkCard
                          : FacebookTheme.lightCard),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: selected
                        ? FacebookTheme.facebookBlue
                        : (Theme.of(context).brightness == Brightness.dark
                            ? FacebookTheme.darkBorder
                            : FacebookTheme.lightBorder),
                    ),
                    boxShadow: selected ? FacebookTheme.facebookShadow : null,
                  ),
                  child: ChoiceChip(
                    label: Text(f, style: FacebookTheme.facebookBody2.copyWith(
                      color: selected
                        ? Colors.white
                        : (Theme.of(context).brightness == Brightness.dark
                            ? FacebookTheme.darkText
                            : FacebookTheme.lightText),
                      fontWeight: selected ? FontWeight.w600 : FontWeight.normal,
                    )),
                    selected: selected,
                    backgroundColor: Colors.transparent,
                    selectedColor: Colors.transparent,
                    side: BorderSide.none,
                    onSelected: (_) => setState(() => filter = f),
                  ),
                );
              },
              separatorBuilder: (_, __) => const SizedBox(width: 12),
              itemCount: filters.length,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: list.isNotEmpty
                ? ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    itemCount: list.length,
                    itemBuilder: (_, i) => GestureDetector(
                      onTap: () => Navigator.of(context).push(
                        MaterialPageRoute(builder: (_) => OrderDetailsScreen(orderId: list[i].id)),
                      ),
                      child: _OrderTile(order: list[i]),
                    ),
                  )
                : Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: const Color(0xFF1F1F1F),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: const Icon(Icons.inbox_outlined, color: Colors.white54, size: 48),
                        ),
                        const SizedBox(height: 16),
                        const Text('No orders for this status.', style: TextStyle(color: Colors.white70, fontSize: 18)),
                      ],
                    ),
                  ),
          ),
        ]);
      }),
    );
  }
}

class _OrderTile extends StatelessWidget {
  final Order order;
  const _OrderTile({required this.order});

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return FacebookWidgets.facebookCard(
      margin: const EdgeInsets.only(bottom: 12),
      isDark: isDark,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            clipBehavior: Clip.none,
            children: [
              FacebookWidgets.deviceIcon('smartphone', size: 28),
              Positioned(
                bottom: -4,
                right: -4,
                child: CircleAvatar(
                  radius: 12,
                  backgroundColor: FacebookTheme.facebookBlue,
                  backgroundImage: (order.customer.avatarUrl != null && order.customer.avatarUrl!.isNotEmpty)
                      ? FileImage(File(order.customer.avatarUrl!))
                      : null,
                  child: (order.customer.avatarUrl == null || order.customer.avatarUrl!.isEmpty)
                      ? Text(
                          order.customer.name.isNotEmpty ? order.customer.name[0].toUpperCase() : '?',
                          style: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.w600),
                        )
                      : null,
                ),
              ),
            ],
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(children: [
                  Expanded(
                    child: Text(
                      order.deviceModel,
                      style: FacebookTheme.facebookHeading3.copyWith(
                        color: isDark ? FacebookTheme.darkText : FacebookTheme.lightText,
                      ),
                    ),
                  ),
                  FacebookWidgets.statusBadge(order.status.name),
                  const SizedBox(width: 8),
                  FacebookWidgets.priorityIndicator(order.priority.name),
                ]),
                const SizedBox(height: 8),
                Text(
                  '${order.customer.name} • ${order.customer.phone}',
                  style: FacebookTheme.facebookBody2.copyWith(
                    color: isDark ? FacebookTheme.darkSecondaryText : FacebookTheme.lightSecondaryText,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  order.issueDescription,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: FacebookTheme.facebookBody2.copyWith(
                    color: isDark ? FacebookTheme.darkSecondaryText : FacebookTheme.lightSecondaryText,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: FacebookTheme.successGreen,
                        borderRadius: FacebookTheme.facebookRadius,
                        boxShadow: FacebookTheme.facebookShadow,
                      ),
                      child: Text(
                        'Est. ₹${order.estimatedCost.toStringAsFixed(0)}',
                        style: FacebookTheme.facebookCaption.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Row(children: [
                      IconButton(
                        onPressed: () => Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (_) => NewOrderScreen(existingOrder: order),
                          ),
                        ),
                        icon: Icon(Icons.edit_outlined,
                          color: FacebookTheme.facebookBlue, size: 20),
                        tooltip: 'Edit Order',
                      ),
                      IconButton(
                        onPressed: () => _showDeleteDialog(context, order),
                        icon: const Icon(Icons.delete_outline,
                          color: FacebookTheme.errorRed, size: 20),
                        tooltip: 'Delete Order',
                      ),
                      IconButton(
                        onPressed: () => Navigator.of(context).push(
                          MaterialPageRoute(builder: (_) => OrderDetailsScreen(orderId: order.id)),
                        ),
                        icon: Icon(Icons.arrow_forward_ios,
                          color: isDark ? FacebookTheme.darkSecondaryText : FacebookTheme.lightSecondaryText,
                          size: 16),
                        tooltip: 'View Details',
                      ),
                    ]),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, Order order) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1F1F1F),
          title: const Text('Delete Order', style: TextStyle(color: Colors.white)),
          content: Text(
            'Are you sure you want to delete order ${order.id}?\nThis action cannot be undone.',
            style: const TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel', style: TextStyle(color: Colors.white70)),
            ),
            Container(
              decoration: BoxDecoration(
                gradient: const LinearGradient(colors: [Colors.red, Color(0xFFB71C1C)]),
                borderRadius: BorderRadius.circular(8),
              ),
              child: TextButton(
                onPressed: () {
                  Navigator.of(context).pop(); // Close dialog
                  _deleteOrder(context, order);
                },
                style: TextButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Delete'),
              ),
            ),
          ],
        );
      },
    );
  }

  void _deleteOrder(BuildContext context, Order order) {
    final appState = Provider.of<AppState>(context, listen: false);
    appState.deleteOrder(order.id);

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Order deleted successfully'),
        backgroundColor: Colors.red,
      ),
    );
  }
}


