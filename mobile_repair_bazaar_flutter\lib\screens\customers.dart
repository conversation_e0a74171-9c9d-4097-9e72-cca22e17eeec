import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:camera/camera.dart';


import 'camera_capture.dart';
import '../app_state.dart';
import '../models.dart';
import '../utils/links.dart';
import 'add_customer.dart';
import 'customer_profile.dart';

class CustomersScreen extends StatefulWidget {
  const CustomersScreen({super.key});

  @override
  State<CustomersScreen> createState() => _CustomersScreenState();
}

class _CustomersScreenState extends State<CustomersScreen> {
  final searchController = TextEditingController();
  String searchQuery = '';

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(builder: (_, state, __) {
      final customers = state.customers.where((customer) {
        if (searchQuery.isEmpty) return true;
        String normalize(String s) => s.toLowerCase().replaceAll(' ', '');
        final q = normalize(searchQuery);
        final name = normalize(customer.name);
        final phone = customer.phone.replaceAll(' ', '');
        final addr = customer.address == null ? '' : normalize(customer.address!);
        return name.contains(q) || phone.contains(q) || addr.contains(q);
      }).toList();

      return Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.black, Color(0xFF0A0A0A)],
          ),
        ),
        child: Scaffold(
          backgroundColor: Colors.transparent,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            title: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFFE50914),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.group_rounded, color: Colors.white, size: 20),
                ),
                const SizedBox(width: 12),
                const Text('Customers', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white)),
              ],
            ),
            actions: [
              Container(
                margin: const EdgeInsets.only(right: 16),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(colors: [Color(0xFFE50914), Color(0xFFB20710)]),
                  borderRadius: BorderRadius.circular(12),
                ),
                  child: IconButton(
                    icon: const Icon(Icons.add, color: Colors.white),
                    onPressed: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(builder: (_) => const AddCustomerScreen()),
                      );
                    },
                  ),
                ),
              ],
            ),
            body: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFF1F1F1F),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: Colors.white12),
                    ),
                    child: TextField(
                      controller: searchController,
                      style: const TextStyle(color: Colors.white),
                      decoration: InputDecoration(
                        hintText: 'Search customers...',
                        hintStyle: const TextStyle(color: Colors.white54),
                        prefixIcon: const Icon(Icons.search, color: Colors.white70),
                        suffixIcon: searchQuery.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear, color: Colors.white70),
                                onPressed: () {
                                  searchController.clear();
                                  setState(() => searchQuery = '');
                                },
                              )
                            : null,
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.all(16),
                      ),
                      onChanged: (value) => setState(() => searchQuery = value),
                    ),
                  ),
                ),
                Expanded(
                  child: customers.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                padding: const EdgeInsets.all(24),
                                decoration: BoxDecoration(
                                  color: const Color(0xFF1F1F1F),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: const Icon(Icons.people_outline, color: Colors.white54, size: 48),
                              ),
                              const SizedBox(height: 16),
                              const Text('No customers found.', style: TextStyle(color: Colors.white70, fontSize: 18)),
                            ],
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          itemCount: customers.length,
                          itemBuilder: (_, i) => _CustomerTile(customer: customers[i]),
                        ),
                ),
              ],
            ),
          ),
        );
    });
  }

  void _showAddCustomerDialog(BuildContext context, AppState state) {
    final nameController = TextEditingController();
    final phoneController = TextEditingController();
    final addressController = TextEditingController();
    final formKey = GlobalKey<FormState>();
    File? avatar;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(builder: (context, setState) {
        return AlertDialog(
          title: const Text('Add New Customer'),
          content: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Avatar preview + buttons
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 28,
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        backgroundImage: avatar != null ? FileImage(avatar!) : null,
                        child: avatar == null
                            ? const Icon(Icons.person, color: Colors.white)
                            : null,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Wrap(spacing: 8, runSpacing: 8, children: [
                          if (!kIsWeb && !(Platform.isWindows || Platform.isLinux || Platform.isMacOS))
                            OutlinedButton.icon(
                              onPressed: () async {
                                final path = await Navigator.of(context).push<String>(
                                  MaterialPageRoute(builder: (_) => const CameraCaptureScreen(initialLens: CameraLensDirection.front)),
                                );
                                if (path != null) setState(() => avatar = File(path));
                              },
                              icon: const Icon(Icons.photo_camera),
                              label: const Text('Live Camera'),
                            ),
                          OutlinedButton.icon(
                            onPressed: () async {
                              final picker = ImagePicker();
                              final x = await picker.pickImage(source: ImageSource.gallery, imageQuality: 85);
                              if (x != null) setState(() => avatar = File(x.path));
                            },
                            icon: const Icon(Icons.photo_library),
                            label: const Text('Gallery'),
                          ),
                        ]),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  TextFormField(
                    controller: nameController,
                    decoration: const InputDecoration(labelText: 'Full Name'),
                    validator: (v) => (v == null || v.isEmpty) ? 'Required' : null,
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: phoneController,
                    decoration: const InputDecoration(labelText: 'Phone Number'),
                    keyboardType: TextInputType.phone,
                    validator: (v) => (v == null || v.isEmpty) ? 'Required' : null,
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: addressController,
                    decoration: const InputDecoration(labelText: 'Address (Optional)'),
                    maxLines: 2,
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            FilledButton(
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  final customer = Customer(
                    id: 'CUST-${DateTime.now().millisecondsSinceEpoch}',
                    name: nameController.text.trim(),
                    phone: phoneController.text.trim(),
                    address: addressController.text.trim().isEmpty ? null : addressController.text.trim(),
                    avatarUrl: avatar?.path,
                  );
                  state.addCustomer(customer);
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Customer ${customer.name} added successfully')),
                  );
                }
              },
              child: const Text('Add'),
            ),
          ],
        );
      }),
    );
  }
}

class _CustomerTile extends StatelessWidget {
  final Customer customer;
  const _CustomerTile({required this.customer});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).colorScheme.primary,
          backgroundImage: (customer.avatarUrl != null && customer.avatarUrl!.isNotEmpty)
              ? FileImage(File(customer.avatarUrl!))
              : null,
          child: (customer.avatarUrl == null || customer.avatarUrl!.isEmpty)
              ? Text(
                  customer.name[0].toUpperCase(),
                  style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                )
              : null,
        ),
        title: Text(customer.name, style: const TextStyle(fontWeight: FontWeight.w600)),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(customer.phone),
            if (customer.address != null) Text(customer.address!, style: TextStyle(color: Colors.grey.shade600)),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.phone),
              tooltip: 'Call',
              onPressed: () => launchTel(customer.phone),
            ),
            IconButton(
              icon: const Icon(Icons.message),
              tooltip: 'WhatsApp',
              onPressed: () => launchWhatsAppText(customer.phone, 'Hello ${customer.name}!'),
            ),
            PopupMenuButton(
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: ListTile(
                    leading: Icon(Icons.edit),
                    title: Text('Edit'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
              onSelected: (value) {
                if (value == 'edit') {
                  _showEditCustomerDialog(context, customer);
                }
              },
            ),
          ],
        ),
        onTap: () => _showCustomerDetails(context, customer),
      ),
    );
  }

  void _showCustomerDetails(BuildContext context, Customer customer) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => CustomerProfileScreen(customerId: customer.id),
      ),
    );
  }

  void _showEditCustomerDialog(BuildContext context, Customer customer) {
    final nameController = TextEditingController(text: customer.name);
    final phoneController = TextEditingController(text: customer.phone);
    final addressController = TextEditingController(text: customer.address ?? '');
    final formKey = GlobalKey<FormState>();
    File? avatar = (customer.avatarUrl != null && customer.avatarUrl!.isNotEmpty)
        ? File(customer.avatarUrl!)
        : null;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: const Text('Edit Customer'),
            content: Form(
              key: formKey,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 28,
                          backgroundColor: Theme.of(context).colorScheme.primary,
                          backgroundImage: avatar != null ? FileImage(avatar!) : null,
                          child: avatar == null
                              ? Text(customer.name[0].toUpperCase(), style: const TextStyle(color: Colors.white))
                              : null,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Wrap(spacing: 8, runSpacing: 8, children: [
                            if (!kIsWeb && !(Platform.isWindows || Platform.isLinux || Platform.isMacOS))
                              OutlinedButton.icon(
                                onPressed: () async {
                                  final path = await Navigator.of(context).push<String>(
                                    MaterialPageRoute(builder: (_) => const CameraCaptureScreen(initialLens: CameraLensDirection.front)),
                                  );
                                  if (path != null) setState(() => avatar = File(path));
                                },
                                icon: const Icon(Icons.photo_camera),
                                label: const Text('Live Camera'),
                              ),
                            OutlinedButton.icon(
                              onPressed: () async {
                                final picker = ImagePicker();
                                final x = await picker.pickImage(source: ImageSource.gallery, imageQuality: 85);
                                if (x != null) setState(() => avatar = File(x.path));
                              },
                              icon: const Icon(Icons.photo_library),
                              label: const Text('Gallery'),
                            ),
                          ]),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    TextFormField(
                      controller: nameController,
                      decoration: const InputDecoration(labelText: 'Full Name'),
                      validator: (v) => (v == null || v.isEmpty) ? 'Required' : null,
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: phoneController,
                      decoration: const InputDecoration(labelText: 'Phone Number'),
                      keyboardType: TextInputType.phone,
                      validator: (v) => (v == null || v.isEmpty) ? 'Required' : null,
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: addressController,
                      decoration: const InputDecoration(labelText: 'Address (Optional)'),
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              FilledButton(
                onPressed: () {
                  if (formKey.currentState!.validate()) {
                    final updatedCustomer = Customer(
                      id: customer.id,
                      name: nameController.text.trim(),
                      phone: phoneController.text.trim(),
                      address: addressController.text.trim().isEmpty ? null : addressController.text.trim(),
                      avatarUrl: avatar?.path ?? customer.avatarUrl,
                    );
                    context.read<AppState>().updateCustomer(updatedCustomer);
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Customer ${updatedCustomer.name} updated successfully')),
                    );
                  }
                },
                child: const Text('Update'),
              ),
            ],
          );
        },
      ),
    );
  }
}
