
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Camera as CameraIcon, X, AlertTriangle } from 'lucide-react';
import Button from './Button';

interface CameraModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCapture: (base64Image: string) => void;
}

const CameraModal: React.FC<CameraModalProps> = ({ isOpen, onClose, onCapture }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const [error, setError] = useState<string | null>(null);

  const stopStream = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
  }, []);

  useEffect(() => {
    if (!isOpen) {
      stopStream();
      return;
    }

    let isMounted = true;

    const startCamera = async () => {
      setError(null);
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ video: { facingMode: 'environment' } });
        if (!isMounted) {
          stream.getTracks().forEach(track => track.stop());
          return;
        }

        streamRef.current = stream;
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
          videoRef.current.play().catch(err => {
            console.error("Video autoplay failed:", err);
            if (isMounted) setError("Could not start video playback.");
          });
        }
      } catch (err: any) {
        console.error("Camera access error:", err);
        if (isMounted) {
            if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
              setError('Camera permission denied. Please enable it in your browser settings.');
            } else {
              setError('Could not access camera. Is it being used by another app?');
            }
        }
      }
    };

    startCamera();

    return () => {
      isMounted = false;
      stopStream();
    };
  }, [isOpen, stopStream]);

  const handleCapture = () => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      const ctx = canvas.getContext('2d');
      ctx?.drawImage(video, 0, 0, video.videoWidth, video.videoHeight);
      const base64Image = canvas.toDataURL('image/jpeg');
      onCapture(base64Image);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div role="dialog" aria-modal="true" className="fixed inset-0 z-[70] bg-black flex flex-col items-center justify-center">
      <video ref={videoRef} autoPlay playsInline className="w-full h-full object-cover" />
      <canvas ref={canvasRef} className="hidden" />

      {error && (
        <div className="absolute inset-0 bg-black/80 flex flex-col items-center justify-center text-center p-4">
            <AlertTriangle className="text-yellow-400 w-16 h-16 mb-4" />
            <h3 className="text-xl font-bold text-white mb-2">Camera Error</h3>
            <p className="text-gray-300">{error}</p>
        </div>
      )}
      
      <div className="absolute top-4 right-4">
        <Button onClick={onClose} variant="secondary" size="sm" className="!rounded-full !p-2 !bg-black/50 hover:!bg-black/70 !text-white">
            <X size={24} />
        </Button>
      </div>
      
      <div className="absolute bottom-8 left-1/2 -translate-x-1/2">
        {!error && (
            <button
                onClick={handleCapture}
                aria-label="Take Photo"
                className="w-20 h-20 rounded-full bg-white/30 border-4 border-white flex items-center justify-center shadow-lg hover:bg-white/50 transition-all"
            >
                <CameraIcon className="text-white w-10 h-10" />
            </button>
        )}
      </div>
    </div>
  );
};

export default CameraModal;
