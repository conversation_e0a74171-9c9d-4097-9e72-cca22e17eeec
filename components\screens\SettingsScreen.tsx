
import React from 'react';
import Card from '../ui/Card';
import { ChevronRight, Palette, Bell, Lock, Store, Users, Clock, Database, LogOut, Info, Check, KeyRound } from 'lucide-react';
import { useApp } from '../../context/AppContext';
import Button from '../ui/Button';
import Switch from '../ui/Switch';

interface SettingsItemProps {
  icon: React.ReactNode;
  title: string;
  subtitle?: string;
  onClick?: () => void;
  accessory?: 'chevron' | 'switch' | 'none';
  switchState?: boolean;
  onSwitchChange?: (state: boolean) => void;
}

const SettingsItem: React.FC<SettingsItemProps> = ({
  icon,
  title,
  subtitle,
  onClick,
  accessory = 'chevron',
  switchState = false,
  onSwitchChange = () => {},
}) => {
  const renderAccessory = () => {
    switch (accessory) {
      case 'chevron':
        return <ChevronRight className="text-gray-400" size={20} />;
      case 'switch':
        return <Switch enabled={switchState} onChange={onSwitchChange} aria-label={title} />;
      case 'none':
        return null;
    }
  };

  const content = (
      <>
        <div className="flex items-center gap-4">
          <div className="bg-accent-light text-accent p-3 rounded-lg">
            {icon}
          </div>
          <div>
            <p className="font-semibold text-gray-800">{title}</p>
            {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
          </div>
        </div>
        {renderAccessory()}
      </>
  );
  
  const baseClasses = "flex items-center justify-between w-full py-4 px-4 border-b border-gray-100 last:border-b-0";
  const interactiveClasses = onClick ? 'cursor-pointer hover:bg-gray-50 transition-colors duration-150 text-left' : '';

  if (onClick) {
    return <button onClick={onClick} className={`${baseClasses} ${interactiveClasses}`}>{content}</button>;
  }

  return <div className={`${baseClasses} ${interactiveClasses}`}>{content}</div>;
};

const timeAgo = (dateString: string | null): string => {
    if (!dateString) return "never";
    const date = new Date(dateString);
    const now = new Date();
    const seconds = Math.round((now.getTime() - date.getTime()) / 1000);
    const minutes = Math.round(seconds / 60);
    const hours = Math.round(minutes / 60);
    const days = Math.round(hours / 24);

    if (seconds < 5) return "just now";
    if (seconds < 60) return `${seconds}s ago`;
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
};


const SettingsScreen: React.FC = () => {
    const { 
        theme: [currentTheme, setTheme], 
        technicians,
        setActiveScreen,
        notificationsEnabled: [notificationsEnabled, setNotificationsEnabled],
        appLockEnabled: [appLockEnabled, setAppLockEnabled],
        lastSync,
        syncData,
        logout,
        owner,
        shopProfile,
        changePin
    } = useApp();

    const themes = [
      { name: 'Pink', value: 'theme-pink', color: 'bg-[#f43397]' },
      { name: 'Blue', value: 'theme-blue', color: 'bg-[#3b82f6]' },
      { name: 'Green', value: 'theme-green', color: 'bg-[#16a34a]' },
    ];
    
    const SettingsCard: React.FC<{title: string, children: React.ReactNode}> = ({ title, children }) => (
        <Card className="!p-0 overflow-hidden">
            <div className="px-4 pt-4 pb-2">
                <h3 className="text-sm font-bold text-gray-500 uppercase tracking-wider">{title}</h3>
            </div>
            <div>
                {children}
            </div>
        </Card>
    );

    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-gray-800">Settings</h2>

        <Card className="bg-gradient-to-br from-accent/5 to-white">
          <div className="flex items-center gap-4">
            {shopProfile.avatarUrl ? (
              <img src={shopProfile.avatarUrl} alt={shopProfile.name} className="w-16 h-16 rounded-full object-cover ring-2 ring-accent/20"/>
            ) : (
              <div className="w-16 h-16 rounded-full bg-accent-light flex items-center justify-center ring-2 ring-accent/20">
                <Store className="w-8 h-8 text-accent"/>
              </div>
            )}
            <div>
              <p className="text-2xl font-bold text-gray-800">{shopProfile.name}</p>
              {owner && <p className="text-accent font-semibold">{owner.role}</p>}
            </div>
          </div>
        </Card>

        <SettingsCard title="General">
            <SettingsItem icon={<Store size={20} />} title="Shop Profile" subtitle="Name, address, contact" accessory="chevron" onClick={() => setActiveScreen('ShopProfile')} />
            <SettingsItem icon={<Clock size={20} />} title="Business Hours" subtitle="Set your opening times" accessory="chevron" onClick={() => setActiveScreen('BusinessHours')} />
        </SettingsCard>
        
        <SettingsCard title="Appearance & Data">
             <SettingsItem icon={<Palette size={20} />} title="App Theme" accessory="none" />
             <div className="flex justify-around items-center px-4 pt-2 pb-4 border-b border-gray-100">
                {themes.map(theme => (
                    <button key={theme.value} onClick={() => setTheme(theme.value)} className="flex flex-col items-center gap-2 group" aria-label={`Set theme to ${theme.name}`}>
                        <div className={`w-14 h-14 rounded-full ${theme.color} border-4 transition-all flex items-center justify-center
                            ${currentTheme === theme.value ? 'border-accent ring-2 ring-accent/30' : 'border-white group-hover:border-gray-200'}`}>
                            {currentTheme === theme.value && <Check className="text-white" size={28} />}
                        </div>
                        <span className={`text-sm font-medium transition-colors ${currentTheme === theme.value ? 'text-accent' : 'text-gray-600 group-hover:text-gray-900'}`}>{theme.name}</span>
                    </button>
                ))}
            </div>
            <SettingsItem icon={<Database size={20} />} title="Data & Sync" subtitle={`Last sync: ${timeAgo(lastSync)}`} accessory="chevron" onClick={syncData} />
        </SettingsCard>

        <SettingsCard title="Security & Notifications">
             <SettingsItem 
              icon={<Bell size={20} />} 
              title="Notifications" 
              subtitle={notificationsEnabled ? 'On' : 'Off'} 
              accessory="switch"
              switchState={notificationsEnabled}
              onSwitchChange={setNotificationsEnabled}
            />
            <SettingsItem 
              icon={<Lock size={20} />} 
              title="App Lock" 
              subtitle={appLockEnabled ? "Enabled" : "Disabled"} 
              accessory="switch"
              switchState={appLockEnabled}
              onSwitchChange={setAppLockEnabled}
            />
            {appLockEnabled && (
                <SettingsItem
                    icon={<KeyRound size={20} />}
                    title="Change PIN"
                    accessory="chevron"
                    onClick={changePin}
                />
            )}
        </SettingsCard>

         <SettingsCard title="More">
              <SettingsItem icon={<Info size={20} />} title="About" subtitle="Version 1.0.0" accessory="chevron" onClick={() => setActiveScreen('About')} />
        </SettingsCard>

        <Button onClick={logout} variant="ghost" className="w-full !text-red-600 hover:!bg-red-50 !font-semibold">
          <LogOut size={18} />
          Logout
        </Button>
      </div>
    );
};

export default SettingsScreen;