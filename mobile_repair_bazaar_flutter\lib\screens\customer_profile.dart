import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../app_state.dart';
import '../models.dart';
import '../utils/links.dart';
import 'order_details.dart';

class CustomerProfileScreen extends StatelessWidget {
  final String customerId;
  const CustomerProfileScreen({super.key, required this.customerId});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(builder: (_, state, __) {
      final customer = state.customers.where((c) => c.id == customerId).cast<Customer?>().isEmpty
          ? null
          : state.customers.firstWhere((c) => c.id == customerId);
      if (customer == null) {
        return const Scaffold(body: Center(child: Text('Customer not found')));
      }

      // Compute stats similar to web app
      final customerOrders = state.orders.where((o) => o.customer.id == customer.id).toList();
      final totalSpent = customerOrders.fold<double>(0, (s, o) => s + o.estimatedCost);
      final lastVisit = customerOrders.isEmpty
          ? '-'
          : _formatDate(customerOrders.map((o) => o.createdAt).reduce((a, b) => a.isAfter(b) ? a : b));

      final nameInitial = customer.name.isNotEmpty ? customer.name[0].toUpperCase() : '?';

      return Scaffold(
        appBar: AppBar(title: const Text('Customer Profile')),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      CircleAvatar(
                        radius: 40,
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        backgroundImage: (customer.avatarUrl != null && customer.avatarUrl!.isNotEmpty)
                            ? FileImage(File(customer.avatarUrl!))
                            : null,
                        child: (customer.avatarUrl == null || customer.avatarUrl!.isEmpty)
                            ? Text(nameInitial, style: const TextStyle(color: Colors.white, fontSize: 28, fontWeight: FontWeight.bold))
                            : null,
                      ),
                      const SizedBox(height: 12),
                      Text(customer.name, style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                      const SizedBox(height: 4),
                      Text(customer.phone, style: TextStyle(color: Colors.grey.shade700)),
                      if (customer.address != null) ...[
                        const SizedBox(height: 4),
                        Text(customer.address!, style: TextStyle(color: Colors.grey.shade600), textAlign: TextAlign.center),
                      ],
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: FilledButton.icon(
                              onPressed: () => launchTel(customer.phone),
                              icon: const Icon(Icons.phone),
                              label: const Text('Call'),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: () => launchWhatsAppText(customer.phone, 'Hello ${customer.name}!'),
                              icon: const Icon(Icons.message),
                              label: const Text('WhatsApp'),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 12),

              // Stats grid
              GridView(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  mainAxisSpacing: 8,
                  crossAxisSpacing: 8,
                  childAspectRatio: 2.0,
                ),
                children: [
                  _StatCard(title: 'Total Repairs', value: '${customerOrders.length}', color: Colors.blue),
                  _StatCard(title: 'Total Spent', value: '₹${totalSpent.toStringAsFixed(0)}', color: Colors.green),
                  _StatCard(title: 'Last Visit', value: lastVisit, color: Colors.orange),
                ],
              ),

              const SizedBox(height: 12),

              // Recent orders
              if (customerOrders.isNotEmpty) ...[
                const Text('Orders', style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                ...customerOrders.map((o) => _OrderTile(order: o)),
                const SizedBox(height: 12),
              ],

              // Notes (editable)
              _NotesSection(customer: customer),
            ],
          ),
        ),
      );
    });
  }

  static String _formatDate(DateTime d) => '${d.day.toString().padLeft(2, '0')}/${d.month.toString().padLeft(2, '0')}/${d.year}';
}

class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final Color color;
  const _StatCard({required this.title, required this.value, required this.color});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.25)),
      ),
      padding: const EdgeInsets.all(10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(title, style: TextStyle(color: Colors.grey.shade700, fontSize: 11, fontWeight: FontWeight.w600)),
          const SizedBox(height: 4),
          Text(value, style: TextStyle(color: color, fontSize: 16, fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }
}

class _OrderTile extends StatelessWidget {
  final Order order;
  const _OrderTile({required this.order});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        title: Text('${order.brand ?? ''} ${order.deviceModel}'.trim()),
        subtitle: Text(order.issueDescription),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text('₹${order.estimatedCost.toStringAsFixed(0)}', style: const TextStyle(fontWeight: FontWeight.bold)),
            Text(order.status.name),
          ],
        ),
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (_) => OrderDetailsScreen(orderId: order.id)),
          );
        },
      ),
    );
  }
}

class _NotesSection extends StatefulWidget {
  final Customer customer;
  const _NotesSection({required this.customer});

  @override
  State<_NotesSection> createState() => _NotesSectionState();
}

class _NotesSectionState extends State<_NotesSection> {
  late final TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.customer.notes ?? '');
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Notes', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            TextField(
              controller: _controller,
              maxLines: 4,
              decoration: const InputDecoration(
                hintText: 'Add private notes about this customer...',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 8),
            Align(
              alignment: Alignment.centerRight,
              child: FilledButton.icon(
                onPressed: () async {
                  final updated = Customer(
                    id: widget.customer.id,
                    name: widget.customer.name,
                    phone: widget.customer.phone,
                    address: widget.customer.address,
                    notes: _controller.text.trim().isEmpty ? null : _controller.text.trim(),
                    avatarUrl: widget.customer.avatarUrl,
                  );
                  final appState = context.read<AppState>();
                  await appState.updateCustomer(updated);
                  if (!mounted) return;
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Notes saved')),
                  );
                },
                icon: const Icon(Icons.save),
                label: const Text('Save Notes'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

