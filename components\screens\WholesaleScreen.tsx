
import React, { useState, useEffect } from 'react';
import { useApp } from '../../context/AppContext';
import type { WholesaleItem } from '../../types';
import Card from '../ui/Card';
import Button from '../ui/Button';
import Modal from '../ui/Modal';
import Input from '../ui/Input';
import { PlusCircle, Edit, Trash2, Save, Truck, Tag, Archive, DollarSign, Package, Briefcase, Barcode } from 'lucide-react';

const defaultFormData: Omit<WholesaleItem, 'id'> = {
    name: '',
    category: '',
    supplier: '',
    costPrice: 0,
    wholesalePrice: 0,
    moq: 1,
    stock: 0,
};

const ItemCard: React.FC<{ item: WholesaleItem; onEdit: () => void; onDelete: () => void; }> = ({ item, onEdit, onDelete }) => {
    return (
        <Card className="mb-4">
            <div className="flex justify-between items-start">
                <div>
                    <p className="font-bold text-gray-800">{item.name}</p>
                    <p className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full inline-block mt-1">{item.category || 'Uncategorized'}</p>
                </div>
                <div className="flex items-center gap-1">
                    <Button variant="ghost" size="sm" className="!text-gray-600 hover:!bg-gray-100 !p-2" onClick={onEdit}>
                        <Edit size={16} />
                    </Button>
                    <Button variant="ghost" size="sm" className="!text-red-600 hover:!bg-red-50 !p-2" onClick={onDelete}>
                        <Trash2 size={16} />
                    </Button>
                </div>
            </div>
            <div className="mt-4 space-y-3">
                 <div className="flex items-center text-sm text-gray-600 gap-2">
                    <Truck size={16} className="text-accent"/>
                    <span className="font-medium">Supplier:</span>
                    <span>{item.supplier || 'N/A'}</span>
                 </div>
                 <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="bg-gray-50 p-2 rounded-lg">
                        <p className="text-xs text-gray-500">Cost / Wholesale</p>
                        <p className="font-semibold text-gray-800">₹{item.costPrice} / ₹{item.wholesalePrice}</p>
                    </div>
                    <div className="bg-gray-50 p-2 rounded-lg">
                        <p className="text-xs text-gray-500">Stock / MOQ</p>
                        <p className="font-semibold text-gray-800">{item.stock} units / {item.moq} units</p>
                    </div>
                 </div>
            </div>
        </Card>
    );
};


const WholesaleScreen: React.FC = () => {
    const { wholesaleInventory, addWholesaleItem, updateWholesaleItem, deleteWholesaleItem, setToast, filterQuery } = useApp();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [editingItem, setEditingItem] = useState<WholesaleItem | null>(null);
    const [formData, setFormData] = useState<Omit<WholesaleItem, 'id'>>(defaultFormData);

    useEffect(() => {
        if (editingItem) {
            setFormData(editingItem);
        } else {
            setFormData(defaultFormData);
        }
    }, [editingItem]);

    const handleOpenModal = (item: WholesaleItem | null) => {
        setEditingItem(item);
        setIsModalOpen(true);
    };

    const handleCloseModal = () => {
        setIsModalOpen(false);
        setEditingItem(null);
        setFormData(defaultFormData);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value, type } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: type === 'number' ? parseFloat(value) || 0 : value,
        }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!formData.name || !formData.category || !formData.supplier) {
            setToast('Please fill out at least Name, Category, and Supplier.');
            return;
        }

        if (editingItem) {
            updateWholesaleItem({ ...formData, id: editingItem.id });
        } else {
            addWholesaleItem(formData);
        }
        handleCloseModal();
    };

    const filteredItems = wholesaleInventory.filter(item => {
        if (!filterQuery) return true;
        const lowerQuery = filterQuery.toLowerCase();
        return (
            item.name.toLowerCase().includes(lowerQuery) ||
            item.category.toLowerCase().includes(lowerQuery) ||
            item.supplier.toLowerCase().includes(lowerQuery)
        );
    });

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold text-gray-800">Wholesale Inventory</h2>
                <Button size="sm" onClick={() => handleOpenModal(null)}>
                    <PlusCircle size={16} />
                    Add Item
                </Button>
            </div>

            <Modal isOpen={isModalOpen} onClose={handleCloseModal} title={editingItem ? "Edit Wholesale Item" : "Add Wholesale Item"} size="lg">
                <form onSubmit={handleSubmit} className="space-y-4">
                    <Input label="Item Name" name="name" value={formData.name} onChange={handleChange} icon={<Package size={18} />} required placeholder="e.g., Bulk iPhone Screens" />
                    <Input label="Category" name="category" value={formData.category} onChange={handleChange} icon={<Tag size={18} />} required placeholder="e.g., Display Units" />
                    <Input label="Supplier" name="supplier" value={formData.supplier} onChange={handleChange} icon={<Truck size={18} />} required placeholder="e.g., China Gadgets Inc." />
                    <div className="grid grid-cols-2 gap-4">
                        <Input label="Cost Price (₹)" name="costPrice" type="number" value={formData.costPrice.toString()} onChange={handleChange} icon={<DollarSign size={18} />} />
                        <Input label="Wholesale Price (₹)" name="wholesalePrice" type="number" value={formData.wholesalePrice.toString()} onChange={handleChange} icon={<DollarSign size={18} />} />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                        <Input label="Stock (Units)" name="stock" type="number" value={formData.stock.toString()} onChange={handleChange} icon={<Archive size={18} />} />
                        <Input label="MOQ (Units)" name="moq" type="number" value={formData.moq.toString()} onChange={handleChange} icon={<Barcode size={18} />} />
                    </div>
                    <div className="flex justify-end pt-4">
                        <Button type="submit"><Save size={18} />Save Item</Button>
                    </div>
                </form>
            </Modal>

            <div>
                {filteredItems.length > 0 ? (
                    filteredItems.map(item => (
                        <ItemCard 
                            key={item.id} 
                            item={item}
                            onEdit={() => handleOpenModal(item)}
                            onDelete={() => deleteWholesaleItem(item.id)}
                        />
                    ))
                ) : (
                    <Card className="text-center text-gray-500 py-8">
                        {filterQuery ? `No items found for "${filterQuery}"` : 'No wholesale items found. Add one to get started!'}
                    </Card>
                )}
            </div>

        </div>
    );
};

export default WholesaleScreen;
