
import React, { useState, useRef, ChangeEvent, useEffect, useMemo } from 'react';
import { useApp } from '../../context/AppContext';
import { JobPriority, Order, UsedPart, InventoryItem } from '../../types';
import Card from '../ui/Card';
import Button from '../ui/Button';
import Input from '../ui/Input';
import TextArea from '../ui/TextArea';
import Select from '../ui/Select';
import ActionSheet from '../ui/ActionSheet';
import CameraModal from '../ui/CameraModal';
import Modal from '../ui/Modal';
import { User, Phone, MapPin, Smartphone, Hash, FileText, Camera, Trash2, Tag, DollarSign, Briefcase, X, Save, Package, PlusCircle, AlertCircle, Search } from 'lucide-react';

interface EditFormState {
    customerName: string;
    customerPhone: string;
    customerAddress: string;
    brand: string;
    deviceModel: string;
    imei: string;
    serialNo: string;
    issueDescription: string;
    priority: JobPriority;
    photoUrl: string;
    estimatedCost: string;
    advancePayment: string;
    technicianId: string;
    usedParts: UsedPart[];
    laborCost: string;
}

const resizeImage = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (event) => {
            const img = new Image();
            img.src = event.target?.result as string;
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const MAX_WIDTH = 800;
                const MAX_HEIGHT = 800;
                let { width, height } = img;

                if (width > height) {
                    if (width > MAX_WIDTH) {
                        height *= MAX_WIDTH / width;
                        width = MAX_WIDTH;
                    }
                } else {
                    if (height > MAX_HEIGHT) {
                        width *= MAX_HEIGHT / height;
                        height = MAX_HEIGHT;
                    }
                }
                canvas.width = width;
                canvas.height = height;
                const ctx = canvas.getContext('2d');
                if (!ctx) return reject(new Error('Could not get canvas context'));
                ctx.drawImage(img, 0, 0, width, height);
                resolve(canvas.toDataURL('image/jpeg', 0.85));
            };
            img.onerror = (error) => reject(error);
        };
        reader.onerror = (error) => reject(error);
    });
};


const EditOrderScreen: React.FC = () => {
    const { orders, activeOrderId, technicians, inventory, updateOrder, setToast, setActiveScreen } = useApp();
    
    const orderToEdit = useMemo(() => orders.find(o => o.id === activeOrderId), [orders, activeOrderId]);

    const [formData, setFormData] = useState<EditFormState | null>(null);
    const [isActionSheetOpen, setActionSheetOpen] = useState(false);
    const [isCameraOpen, setCameraOpen] = useState(false);
    const [isPartsModalOpen, setIsPartsModalOpen] = useState(false);
    const [partSearch, setPartSearch] = useState('');

    const fileInputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        if (orderToEdit) {
            setFormData({
                customerName: orderToEdit.customer.name,
                customerPhone: orderToEdit.customer.phone,
                customerAddress: orderToEdit.customer.address || '',
                brand: orderToEdit.brand || '',
                deviceModel: orderToEdit.deviceModel,
                imei: orderToEdit.imei || '',
                serialNo: orderToEdit.serialNo || '',
                issueDescription: orderToEdit.issueDescription,
                priority: orderToEdit.priority,
                photoUrl: orderToEdit.photoUrl || '',
                estimatedCost: String(orderToEdit.estimatedCost),
                advancePayment: String(orderToEdit.advancePayment),
                technicianId: orderToEdit.technicianId || '',
                usedParts: orderToEdit.usedParts || [],
                laborCost: String(orderToEdit.laborCost || ''),
            });
        }
    }, [orderToEdit]);
    
    const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => prev ? ({...prev, [name]: value}) : null);
    };
    
    const handleFileChange = async (event: ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            try {
                const resizedImage = await resizeImage(file);
                setFormData(prev => prev ? ({ ...prev, photoUrl: resizedImage }) : null);
            } catch (error) {
                console.error("Error resizing image:", error);
                setToast("Could not process image.");
            }
        }
        event.target.value = '';
    };
    
    const handleCapture = (base64Image: string) => {
        setFormData(prev => prev ? ({ ...prev, photoUrl: base64Image }) : null);
        setCameraOpen(false);
    };

    const handleAddPart = (item: InventoryItem, quantity: number) => {
        if (!formData) return;
        if (quantity <= 0) {
            setToast('Quantity must be positive.');
            return;
        }

        const existingPart = formData.usedParts?.find(p => p.inventoryItemId === item.id);
        const currentUsedQuantity = existingPart?.quantity || 0;

        if (quantity + currentUsedQuantity > item.quantity) {
            setToast(`Cannot use more than available in stock (${item.quantity}).`);
            return;
        }

        let newParts: UsedPart[];
        if (existingPart) {
            newParts = formData.usedParts.map(p => p.inventoryItemId === item.id ? { ...p, quantity: p.quantity + quantity } : p);
        } else {
            newParts = [...formData.usedParts, {
                inventoryItemId: item.id,
                name: item.name,
                quantity,
                sellingPrice: item.sellingPrice
            }];
        }

        setFormData(prev => prev ? { ...prev, usedParts: newParts } : null);
        setToast(`${item.name} (x${quantity}) added.`);
    };

    const handleRemovePart = (inventoryItemId: string) => {
        setFormData(prev => {
            if (!prev) return null;
            const newParts = prev.usedParts?.filter(p => p.inventoryItemId !== inventoryItemId);
            return { ...prev, usedParts: newParts };
        });
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!formData || !orderToEdit) return;

        if (!formData.customerName || !formData.customerPhone || !formData.deviceModel || !formData.issueDescription) {
            setToast('Please fill all required fields.');
            return;
        }

        const updatedOrder: Order = {
            ...orderToEdit,
            customer: {
                ...orderToEdit.customer,
                name: formData.customerName,
                phone: formData.customerPhone,
                address: formData.customerAddress,
            },
            brand: formData.brand,
            deviceModel: formData.deviceModel,
            imei: formData.imei,
            serialNo: formData.serialNo,
            issueDescription: formData.issueDescription,
            estimatedCost: parseFloat(formData.estimatedCost) || 0,
            advancePayment: parseFloat(formData.advancePayment) || 0,
            priority: formData.priority,
            photoUrl: formData.photoUrl,
            technicianId: formData.technicianId,
            usedParts: formData.usedParts || [],
            laborCost: parseFloat(formData.laborCost) || 0,
        };

        updateOrder(updatedOrder);
        setToast('Order updated successfully!');
        setActiveScreen('OrderDetails');
    };

    if (!formData || !orderToEdit) {
        return (
            <Card className="text-center p-8">
                <p>Loading order details...</p>
            </Card>
        );
    }
    
    const partsTotal = formData.usedParts.reduce((sum, part) => sum + part.sellingPrice * part.quantity, 0);
    const laborCost = parseFloat(formData.laborCost) || 0;
    const calculatedTotal = partsTotal + laborCost;

    const filteredInventory = inventory.filter(item => item.name.toLowerCase().includes(partSearch.toLowerCase()));

    return (
        <div className="space-y-6">
            <input type="file" accept="image/*" ref={fileInputRef} onChange={handleFileChange} className="hidden" />
            <ActionSheet isOpen={isActionSheetOpen} onClose={() => setActionSheetOpen(false)} title="Add Device Photo">
                <Button variant="ghost" className="w-full !justify-start" onClick={() => {setActionSheetOpen(false); setCameraOpen(true)}}><Camera size={20}/>Take Photo</Button>
                <Button variant="ghost" className="w-full !justify-start" onClick={() => {setActionSheetOpen(false); fileInputRef.current?.click()}}><FileText size={20}/>Choose from Gallery</Button>
            </ActionSheet>
            <CameraModal isOpen={isCameraOpen} onClose={() => setCameraOpen(false)} onCapture={handleCapture} />

            <Modal isOpen={isPartsModalOpen} onClose={() => setIsPartsModalOpen(false)} title="Add Part to Job" size="lg">
                <div className="space-y-4">
                    <Input icon={<Search size={18} />} label="Search Parts" placeholder="e.g., Screen, Battery..." value={partSearch} onChange={e => setPartSearch(e.target.value)} />
                    <div className="max-h-96 overflow-y-auto space-y-2 pr-2">
                        {filteredInventory.length > 0 ? filteredInventory.map(item => {
                            const partInOrder = formData.usedParts.find(p => p.inventoryItemId === item.id);
                            const availableStock = item.quantity - (partInOrder?.quantity || 0);
                            return (
                                <div key={item.id} className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                                    <div className="flex-grow">
                                        <p className="font-semibold text-gray-800">{item.name}</p>
                                        <p className="text-xs text-gray-500">In Stock: {item.quantity} | Available: {availableStock}</p>
                                    </div>
                                    <Button size="sm" onClick={() => handleAddPart(item, 1)} disabled={availableStock <= 0}>
                                        <PlusCircle size={16}/> Add
                                    </Button>
                                </div>
                            );
                        }) : <p className="text-center text-gray-500 p-4">No parts found.</p>}
                    </div>
                </div>
            </Modal>

            <form onSubmit={handleSubmit} className="space-y-6">
                <Card>
                    <h3 className="text-lg font-bold text-gray-700 mb-4">Customer Details</h3>
                    <div className="space-y-4">
                        <Input required name="customerName" label="Full Name" value={formData.customerName} onChange={handleChange} icon={<User size={18} />} />
                        <Input required name="customerPhone" label="Phone Number" type="tel" value={formData.customerPhone} onChange={handleChange} icon={<Phone size={18} />} />
                        <Input name="customerAddress" label="Address" placeholder="Customer's full address" value={formData.customerAddress} onChange={handleChange} icon={<MapPin size={18} />} />
                    </div>
                </Card>

                <Card>
                    <h3 className="text-lg font-bold text-gray-700 mb-4">Device Details</h3>
                    <div className="space-y-4">
                        <Input name="brand" label="Brand (e.g., Apple, Samsung)" value={formData.brand} onChange={handleChange} icon={<Tag size={18} />} />
                        <Input required name="deviceModel" label="Model (e.g., iPhone 13 Pro)" value={formData.deviceModel} onChange={handleChange} icon={<Smartphone size={18} />} />
                        <Input name="imei" label="IMEI Number" value={formData.imei} onChange={handleChange} icon={<Hash size={18} />} />
                        <Input name="serialNo" label="Serial Number" value={formData.serialNo} onChange={handleChange} icon={<Hash size={18} />} />
                    </div>
                </Card>

                <Card>
                    <h3 className="text-lg font-bold text-gray-700 mb-4">Job Details</h3>
                    <div className="space-y-4">
                        <TextArea required name="issueDescription" label="Problem Description" rows={4} value={formData.issueDescription} onChange={handleChange} />
                        <div>
                             <label className="block text-sm font-medium text-gray-700 mb-2">Condition Photo</label>
                             {formData.photoUrl ? (
                                <div className="relative group">
                                    <img src={formData.photoUrl} alt="Device condition" className="w-full h-48 object-cover rounded-lg" />
                                    <button type="button" onClick={() => setFormData(p => p ? ({...p, photoUrl: ''}) : null)} className="absolute top-2 right-2 bg-red-500 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity">
                                        <Trash2 size={18} />
                                    </button>
                                </div>
                             ) : (
                                <Button type="button" variant="secondary" onClick={() => setActionSheetOpen(true)}>
                                    <Camera size={16} /> Add Photo
                                </Button>
                             )}
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                            <div className="flex gap-2">
                                {(Object.keys(JobPriority) as Array<keyof typeof JobPriority>).map(key => (
                                    <button type="button" key={key} onClick={() => setFormData(p => p ? ({ ...p, priority: JobPriority[key]}) : null)} 
                                    className={`flex-1 text-center p-2 rounded-lg border-2 transition-all ${formData.priority === JobPriority[key] ? 'border-accent bg-accent-light' : 'border-gray-200 bg-white hover:border-gray-300'}`}>
                                        <span className={`font-semibold ${formData.priority === JobPriority[key] ? 'text-accent' : 'text-gray-600'}`}>{JobPriority[key]}</span>
                                    </button>
                                ))}
                            </div>
                        </div>
                    </div>
                </Card>
                
                <Card>
                    <h3 className="text-lg font-bold text-gray-700 mb-4">Parts, Financials & Assignment</h3>
                    <div className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Parts Used</label>
                            {formData.usedParts.length > 0 ? (
                                <div className="space-y-2">
                                    {formData.usedParts.map(part => (
                                        <div key={part.inventoryItemId} className="flex items-center gap-2 bg-gray-50 p-2 rounded-md">
                                            <Package size={16} className="text-gray-500"/>
                                            <div className="flex-grow">
                                                <p className="font-medium text-sm text-gray-800">{part.name}</p>
                                                <p className="text-xs text-gray-500">{part.quantity} x ₹{part.sellingPrice}</p>
                                            </div>
                                            <p className="font-semibold text-sm">₹{part.quantity * part.sellingPrice}</p>
                                            <button type="button" onClick={() => handleRemovePart(part.inventoryItemId)} className="p-1 text-red-500 hover:bg-red-100 rounded-full"><Trash2 size={16}/></button>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-sm text-gray-500 text-center py-2 bg-gray-50 rounded-md">No parts added yet.</p>
                            )}
                            <Button type="button" variant="secondary" size="sm" className="mt-2 w-full" onClick={() => setIsPartsModalOpen(true)}>
                                <PlusCircle size={16} /> Add Part
                            </Button>
                        </div>

                        <Input name="laborCost" label="Labor & Service Cost (₹)" type="number" value={formData.laborCost} onChange={handleChange} icon={<DollarSign size={18} />} placeholder="0" />
                        
                        <div className="p-3 bg-accent-light rounded-lg text-center">
                            <p className="text-sm text-accent font-semibold">Calculated Total (Parts + Labor)</p>
                            <p className="text-2xl font-bold text-accent">₹{calculatedTotal.toLocaleString('en-IN')}</p>
                            <p className="text-xs text-gray-500">Use this to set the final estimated cost below.</p>
                        </div>

                        <Input required name="estimatedCost" label="Final Estimated Cost (₹)" type="number" value={formData.estimatedCost} onChange={handleChange} icon={<DollarSign size={18} />} />
                        <Input name="advancePayment" label="Advance Payment (₹)" type="number" value={formData.advancePayment} onChange={handleChange} icon={<DollarSign size={18} />} />
                        <Select name="technicianId" label="Assign Technician" value={formData.technicianId} onChange={handleChange} icon={<Briefcase size={18} />}>
                            <option value="">Assign later</option>
                            {technicians.map(tech => <option key={tech.id} value={tech.id}>{tech.name}</option>)}
                        </Select>
                     </div>
                </Card>

                <div className="pb-4 flex gap-2">
                    <Button type="button" variant="ghost" className="w-full" onClick={() => setActiveScreen('OrderDetails')}>
                        <X size={18} />
                        Cancel
                    </Button>
                    <Button type="submit" size="lg" className="w-full">
                        <Save size={18} />
                        Save Changes
                    </Button>
                </div>
            </form>
        </div>
    );
};

export default EditOrderScreen;
