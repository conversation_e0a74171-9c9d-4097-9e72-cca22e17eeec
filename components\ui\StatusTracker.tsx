import React from 'react';
import { OrderStatus } from '../../types';
import { PackagePlus, SearchCheck, Wrench, ShieldCheck, PackageCheck, CheckCircle2, XCircle, MoreHorizontal } from 'lucide-react';

interface StatusTrackerProps {
  currentStatus: OrderStatus;
  statusFlow: OrderStatus[];
}

const statusInfo: { [key in OrderStatus]: { icon: React.ReactNode, text: string } } = {
  [OrderStatus.Received]: { icon: <PackagePlus size={20} />, text: "Device Received" },
  [OrderStatus.Diagnosing]: { icon: <SearchCheck size={20} />, text: "Diagnosing Issue" },
  [OrderStatus.RepairInProgress]: { icon: <Wrench size={20} />, text: "Repair in Progress" },
  [OrderStatus.QualityCheck]: { icon: <ShieldCheck size={20} />, text: "Quality Check" },
  [OrderStatus.ReadyForDelivery]: { icon: <PackageCheck size={20} />, text: "Ready for Delivery" },
  [OrderStatus.Delivered]: { icon: <CheckCircle2 size={20} />, text: "Delivered" },
  [OrderStatus.Cancelled]: { icon: <XCircle size={20} />, text: "Cancelled" },
};

const StatusTracker: React.FC<StatusTrackerProps> = ({ currentStatus, statusFlow }) => {
  const relevantFlow = currentStatus === OrderStatus.Cancelled ? [OrderStatus.Cancelled] : statusFlow;
  const currentStatusIndex = relevantFlow.indexOf(currentStatus);

  const getStatusItem = (status: OrderStatus, index: number) => {
    const isCompleted = currentStatusIndex > index;
    const isActive = currentStatusIndex === index;
    const isCancelled = currentStatus === OrderStatus.Cancelled && isActive;

    let iconContainerClass = 'bg-gray-200 text-gray-500';
    let textClass = 'text-gray-500';
    let lineClass = 'bg-gray-200';
    let Icon = statusInfo[status]?.icon || <MoreHorizontal size={20} />;

    if (currentStatus === OrderStatus.Delivered) {
      iconContainerClass = 'bg-green-100 text-green-600';
      textClass = 'text-gray-800 font-semibold';
      lineClass = 'bg-green-500';
      Icon = <CheckCircle2 size={20} />;
    } else if (isCompleted) {
      iconContainerClass = 'bg-green-100 text-green-600';
      textClass = 'text-gray-800 font-semibold';
      lineClass = 'bg-green-500';
      Icon = <CheckCircle2 size={20} />;
    } else if (isActive) {
      if (isCancelled) {
        iconContainerClass = 'bg-red-100 text-red-600';
        textClass = 'text-red-600 font-bold';
        Icon = statusInfo[OrderStatus.Cancelled].icon;
      } else {
        iconContainerClass = 'bg-accent-light text-accent ring-4 ring-accent/20';
        textClass = 'text-accent font-bold';
      }
    }
    
    const lineConnector = index < relevantFlow.length - 1 ? (
      <div className={`absolute left-5 top-10 -ml-px h-full w-0.5 ${lineClass}`} />
    ) : null;

    return (
      <li key={status} className="relative flex items-start gap-4">
        {lineConnector}
        <div className={`z-10 flex h-10 w-10 items-center justify-center rounded-full transition-colors ${iconContainerClass}`}>
          {Icon}
        </div>
        <div className="pt-2">
          <p className={`text-sm font-medium transition-colors ${textClass}`}>
            {statusInfo[status]?.text || status}
          </p>
        </div>
      </li>
    );
  };

  return (
    <nav aria-label="Repair progress">
      <ol className="space-y-4">
        {relevantFlow.map(getStatusItem)}
      </ol>
    </nav>
  );
};

export default StatusTracker;
