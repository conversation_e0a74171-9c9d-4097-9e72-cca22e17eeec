
import React from 'react';
import { useApp } from '../../context/AppContext';
import { OrderStatus } from '../../types';
import Card from '../ui/Card';
import Button from '../ui/Button';
import StatusTracker from '../ui/StatusTracker';
import { User, Phone, Smartphone, Hash, FileText, XCircle, ArrowRightCircle, Edit, Package, Wrench as WrenchIcon, Share2 } from 'lucide-react';
import PriorityBadge from '../ui/PriorityBadge';

const InfoRow: React.FC<{ icon: React.ReactNode, label: string, value?: string | number | null }> = ({ icon, label, value }) => (
    <div className="flex items-start gap-3 py-2">
        <div className="text-gray-400 mt-1">{icon}</div>
        <div>
            <p className="text-sm text-gray-500">{label}</p>
            <p className="font-semibold text-gray-800">{value || 'N/A'}</p>
        </div>
    </div>
);

const OrderDetailsScreen: React.FC = () => {
    const { orders, activeOrderId, updateOrderStatus, setActiveScreen, setToast } = useApp();
    const order = orders.find(o => o.id === activeOrderId);

    if (!order) {
        return <Card className="text-center p-8">Order not found.</Card>;
    }

    const statusFlow = [
        OrderStatus.Received,
        OrderStatus.Diagnosing,
        OrderStatus.RepairInProgress,
        OrderStatus.QualityCheck,
        OrderStatus.ReadyForDelivery,
        OrderStatus.Delivered,
    ];

    const currentStatusIndex = statusFlow.indexOf(order.status);
    const nextStatus = statusFlow[currentStatusIndex + 1];
    
    const handleCopyPublicLink = () => {
        if (!order) return;
        const publicUrl = window.location.href.split('#')[0] + '#public/view/' + order.id;
        navigator.clipboard.writeText(publicUrl).then(() => {
            setToast('Public tracking link copied to clipboard!');
        }, (err) => {
            setToast('Failed to copy link.');
            console.error('Could not copy text: ', err);
        });
    };

    const getNextAction = (): { text: string; action: () => void; } | null => {
        if (!nextStatus) return null;
        const textMap = {
            [OrderStatus.Diagnosing]: "Start Diagnosis",
            [OrderStatus.RepairInProgress]: "Begin Repair",
            [OrderStatus.QualityCheck]: "Complete Quality Check",
            [OrderStatus.ReadyForDelivery]: "Mark as Ready for Delivery",
            [OrderStatus.Delivered]: "Mark as Delivered",
        };
        return {
            text: textMap[nextStatus] || `Proceed to ${nextStatus}`,
            action: () => updateOrderStatus(order.id, nextStatus),
        };
    };

    const nextAction = getNextAction();
    const isActionable = order.status !== OrderStatus.Delivered && order.status !== OrderStatus.Cancelled;
    
    const partsTotal = order.usedParts?.reduce((sum, part) => sum + part.sellingPrice * part.quantity, 0) || 0;
    const laborCost = order.laborCost || 0;
    const calculatedTotal = partsTotal + laborCost;
    const amountDue = order.estimatedCost - order.advancePayment;

    return (
        <div className="space-y-6">
            <Card>
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-bold text-gray-700">Job Details</h3>
                    <div className="flex items-center gap-1">
                        <Button size="sm" variant="ghost" title="Copy Public Link" onClick={handleCopyPublicLink} className="!p-2 !text-gray-600 hover:!bg-gray-100">
                           <Share2 size={16} />
                        </Button>
                        {isActionable && (
                            <Button size="sm" variant="secondary" onClick={() => setActiveScreen('EditOrder')}>
                                <Edit size={16} />
                                Edit
                            </Button>
                        )}
                    </div>
                </div>
                <div className="flex justify-between items-center border-t border-gray-100 pt-3">
                    <p className="text-sm text-gray-500">Order ID: {order.id}</p>
                    <PriorityBadge priority={order.priority} />
                </div>
                <div className="mt-2 grid grid-cols-1 sm:grid-cols-2 gap-x-4">
                     <InfoRow icon={<User size={18}/>} label="Customer Name" value={order.customer.name} />
                     <InfoRow icon={<Phone size={18}/>} label="Customer Phone" value={order.customer.phone} />
                </div>
            </Card>

            <Card>
                <h3 className="text-lg font-bold text-gray-700 mb-2">Device Information</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4">
                     <InfoRow icon={<Smartphone size={18}/>} label="Device" value={`${order.brand || ''} ${order.deviceModel}`} />
                     <InfoRow icon={<Hash size={18}/>} label="IMEI / Serial No." value={order.imei || order.serialNo} />
                     <InfoRow icon={<FileText size={18}/>} label="Issue Description" value={order.issueDescription} />
                </div>
            </Card>

            {(order.usedParts && order.usedParts.length > 0) || order.laborCost ? (
                 <Card>
                    <h3 className="text-lg font-bold text-gray-700 mb-4">Parts & Labor</h3>
                    {order.usedParts && order.usedParts.length > 0 && (
                        <div className="mb-4">
                            <h4 className="flex items-center gap-2 text-sm font-semibold text-gray-600 mb-2"><Package size={16}/> Parts Used</h4>
                            <ul className="space-y-2">
                                {order.usedParts.map(part => (
                                    <li key={part.inventoryItemId} className="flex justify-between items-center text-sm p-2 bg-gray-50 rounded-md">
                                        <div>
                                            <p className="font-medium text-gray-800">{part.name}</p>
                                            <p className="text-xs text-gray-500">{part.quantity} x ₹{part.sellingPrice.toLocaleString()}</p>
                                        </div>
                                        <p className="font-semibold text-gray-800">₹{(part.quantity * part.sellingPrice).toLocaleString()}</p>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    )}
                     {order.laborCost && order.laborCost > 0 && (
                        <div>
                            <h4 className="flex items-center gap-2 text-sm font-semibold text-gray-600 mb-2"><WrenchIcon size={16}/> Labor</h4>
                             <div className="flex justify-between items-center text-sm p-2 bg-gray-50 rounded-md">
                                <p className="font-medium text-gray-800">Service & Labor Charges</p>
                                <p className="font-semibold text-gray-800">₹{order.laborCost.toLocaleString()}</p>
                            </div>
                        </div>
                    )}
                </Card>
            ): null}


            <Card>
                <h3 className="text-lg font-bold text-gray-700 mb-4">Financial Summary</h3>
                 <div className="space-y-2 text-sm">
                     <div className="flex justify-between">
                         <span className="text-gray-600">Parts Total</span>
                         <span className="font-medium text-gray-800">₹{partsTotal.toLocaleString()}</span>
                     </div>
                     <div className="flex justify-between">
                         <span className="text-gray-600">Labor Cost</span>
                         <span className="font-medium text-gray-800">₹{laborCost.toLocaleString()}</span>
                     </div>
                     <div className="flex justify-between font-semibold border-t pt-2 mt-1">
                         <span className="text-gray-800">Calculated Total</span>
                         <span className="text-gray-800">₹{calculatedTotal.toLocaleString()}</span>
                     </div>
                     <div className="flex justify-between text-base font-bold text-accent border-t-2 border-dashed border-accent/30 pt-2 mt-2">
                         <span>Final Estimated Cost</span>
                         <span>₹{order.estimatedCost.toLocaleString()}</span>
                     </div>
                     <div className="flex justify-between">
                         <span className="text-green-600">Advance Paid</span>
                         <span className="font-medium text-green-600">- ₹{order.advancePayment.toLocaleString()}</span>
                     </div>
                 </div>
                <div className="mt-4 bg-accent-light text-accent text-center p-3 rounded-lg">
                    <p className="text-sm font-semibold">Amount Due</p>
                    <p className="text-2xl font-bold">₹{amountDue.toLocaleString()}</p>
                </div>
            </Card>

            <Card>
                <h3 className="text-lg font-bold text-gray-700 mb-4">Job Progress</h3>
                <StatusTracker currentStatus={order.status} statusFlow={statusFlow} />
            </Card>

            {isActionable && (
                 <div className="space-y-2 pb-4">
                    {nextAction && (
                        <Button size="lg" className="w-full" onClick={nextAction.action}>
                            {nextAction.text}
                            <ArrowRightCircle size={20} />
                        </Button>
                    )}
                    <Button variant="secondary" className="w-full !bg-red-50 !text-red-700 hover:!bg-red-100" onClick={() => updateOrderStatus(order.id, OrderStatus.Cancelled)}>
                         <XCircle size={18} />
                         Cancel Order
                    </Button>
                 </div>
            )}
        </div>
    );
};

export default OrderDetailsScreen;
