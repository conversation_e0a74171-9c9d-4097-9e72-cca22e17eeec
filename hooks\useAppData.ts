
import { useState, useMemo, useEffect } from 'react';
import useLocalStorage from './useLocalStorage';
import type { Order, Customer, InventoryItem, Technician, ShopProfile, UsedPart, BusinessHour, WholesaleItem, Notification } from '../types';
import { TechnicianRole, OrderStatus, JobPriority } from '../types';
import { GoogleGenAI } from '@google/genai';

const defaultBusinessHours: BusinessHour[] = [
    { day: 'Monday',    isOpen: true,  openTime: '09:00', closeTime: '20:00' },
    { day: 'Tuesday',   isOpen: true,  openTime: '09:00', closeTime: '20:00' },
    { day: 'Wednesday', isOpen: true,  openTime: '09:00', closeTime: '20:00' },
    { day: 'Thursday',  isOpen: true,  openTime: '09:00', closeTime: '20:00' },
    { day: 'Friday',    isOpen: true,  openTime: '09:00', closeTime: '20:00' },
    { day: 'Saturday',  isOpen: true,  openTime: '10:00', closeTime: '18:00' },
    { day: 'Sunday',    isOpen: false, openTime: '09:00', closeTime: '20:00' },
];

const useAppData = () => {
  // Navigation State
  const [activeScreen, setActiveScreen] = useState('Orders');
  const [activeOrderId, setActiveOrderId] = useState<string | null>(null);
  const [activeCustomerId, setActiveCustomerId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState(''); // For global Gemini search
  const [filterQuery, setFilterQuery] = useState(''); // For local on-screen filtering

  // Persisted Data State
  const [orders, setOrders] = useLocalStorage<Order[]>('app_orders', []);
  const [customers, setCustomers] = useLocalStorage<Customer[]>('app_customers', []);
  const [inventory, setInventory] = useLocalStorage<InventoryItem[]>('app_inventory', []);
  const [wholesaleInventory, setWholesaleInventory] = useLocalStorage<WholesaleItem[]>('app_wholesale_inventory', []);
  const [technicians, setTechnicians] = useLocalStorage<Technician[]>('app_technicians', []);
  const [shopProfile, setShopProfile] = useLocalStorage<ShopProfile>('app_shop_profile', {
    name: 'Your Repair Shop',
    phone: '',
    address: '',
    avatarUrl: '',
    bannerUrl: '',
  });
  
  // Persisted Settings State
  const [theme, setTheme] = useLocalStorage<string>('app_theme', 'theme-pink');
  const [notificationsEnabled, setNotificationsEnabled] = useLocalStorage<boolean>('app_notifications', true);
  const [appLockEnabled, setAppLockEnabled] = useLocalStorage<boolean>('app_appLock', false);
  const [appPin, setAppPin] = useLocalStorage<string | null>('app_pin', null);
  const [lastSync, setLastSync] = useLocalStorage<string | null>('app_lastSync', null);
  const [businessHours, setBusinessHours] = useLocalStorage<BusinessHour[]>('app_business_hours', defaultBusinessHours);
  const [notifications, setNotifications] = useLocalStorage<Notification[]>('app_notifications', []);

  // Ephemeral State for UI feedback
  const [toast, setToast] = useState<string | null>(null);
  const [isLocked, setIsLocked] = useState(false);
  
  // Chat State
  const [chatHistory, setChatHistory] = useState<{role: 'user' | 'model', text: string}[]>([
    { role: 'model', text: 'Hello! I am your AI assistant. How can I help with your shop data today?' }
  ]);
  const [isAiResponding, setIsAiResponding] = useState(false);

  // Check lock status on initial load
  useEffect(() => {
    if (appLockEnabled) {
      setIsLocked(true);
    }
  }, []); // Run only on mount

  // Clear local filter when switching main screens
  useEffect(() => {
    setFilterQuery('');
  }, [activeScreen]);


  // Derived state
  const owner = useMemo(() => technicians.find(t => t.role === TechnicianRole.Owner), [technicians]);
  const unreadNotificationsCount = useMemo(() => notifications.filter(n => !n.read).length, [notifications]);

  // --- Notification System ---
  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read'> & { read?: boolean }) => {
    if (!notificationsEnabled) return;
    const newNotification: Notification = {
        ...notification,
        id: `NOTIF-${Date.now()}`,
        timestamp: new Date().toISOString(),
        read: notification.read ?? false
    };
    // Prevent duplicate notifications for the same event
    const existing = notifications.find(n => n.relatedId === newNotification.relatedId && n.message === newNotification.message);
    if (!existing) {
        setNotifications(prev => [newNotification, ...prev].slice(0, 50)); // Keep max 50 notifications
    }
  };

  const markNotificationAsRead = (id: string) => {
      setNotifications(prev => prev.map(n => n.id === id ? { ...n, read: true } : n));
  };

  const markAllNotificationsAsRead = () => {
      setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  };

  const addOrder = (order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newOrder: Order = {
      ...order,
      id: `ORD-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setOrders(prev => [newOrder, ...prev]);
  };
  
  const updateOrder = (updatedOrder: Order) => {
    // Also update the associated customer details in the main customers list
    setCustomers(prev => prev.map(c => c.id === updatedOrder.customer.id ? updatedOrder.customer : c));
    
    setOrders(prev => prev.map(o => o.id === updatedOrder.id ? { ...updatedOrder, updatedAt: new Date() } : o));
  };
  
  const deleteOrder = (id: string) => {
    if (window.confirm('Are you sure you want to delete this order?')) {
        setOrders(prev => prev.filter(o => o.id !== id));
        setToast('Order deleted successfully.');
    }
  };

  const addCustomer = (customer: Omit<Customer, 'id'>) => {
    const newCustomer: Customer = {
      ...customer,
      id: `CUS-${Date.now()}`
    }
    setCustomers(prev => [newCustomer, ...prev]);
  }
  
  const updateCustomer = (updatedCustomer: Customer) => {
    setCustomers(prev => prev.map(c => c.id === updatedCustomer.id ? updatedCustomer : c));
    // Also update customer details within existing orders to maintain data consistency
    setOrders(prevOrders => prevOrders.map(o => {
        if (o.customer.id === updatedCustomer.id) {
            return { ...o, customer: updatedCustomer };
        }
        return o;
    }));
    setToast('Customer profile updated.');
  };

  const deleteCustomer = (id: string) => {
    if (window.confirm('Are you sure you want to delete this customer? Associated orders will not be deleted.')) {
        setCustomers(prev => prev.filter(c => c.id !== id));
        setToast('Customer deleted successfully.');
    }
  };

  const addInventoryItem = (item: Omit<InventoryItem, 'id'>) => {
    const newItem: InventoryItem = {
      ...item,
      id: `INV-${Date.now()}`
    }
    setInventory(prev => [newItem, ...prev]);
  }

  const updateInventoryItem = (updatedItem: InventoryItem) => {
    setInventory(prev => prev.map(i => i.id === updatedItem.id ? updatedItem : i));
  }
  
  const deleteInventoryItem = (id: string) => {
    if (window.confirm('Are you sure you want to delete this inventory item?')) {
        setInventory(prev => prev.filter(i => i.id !== id));
        setToast('Inventory item deleted successfully.');
    }
  };
  
    const addWholesaleItem = (item: Omit<WholesaleItem, 'id'>) => {
    const newItem: WholesaleItem = {
      ...item,
      id: `WINV-${Date.now()}`
    }
    setWholesaleInventory(prev => [newItem, ...prev]);
    setToast('Wholesale item added.');
}

const updateWholesaleItem = (updatedItem: WholesaleItem) => {
    setWholesaleInventory(prev => prev.map(i => i.id === updatedItem.id ? updatedItem : i));
    setToast('Wholesale item updated.');
}

const deleteWholesaleItem = (id: string) => {
    if (window.confirm('Are you sure you want to delete this wholesale item?')) {
        setWholesaleInventory(prev => prev.filter(i => i.id !== id));
        setToast('Wholesale item deleted.');
    }
};

  const addTechnician = (technician: Omit<Technician, 'id'>) => {
    const newTechnician: Technician = {
      ...technician,
      id: `TECH-${Date.now()}`
    }
    setTechnicians(prev => [newTechnician, ...prev]);
  }

  const updateTechnician = (updatedTechnician: Technician) => {
    setTechnicians(prev => prev.map(t => t.id === updatedTechnician.id ? updatedTechnician : t));
    setToast('Technician profile updated.');
  };
  
  const deleteTechnician = (id: string) => {
    const techToDelete = technicians.find(t => t.id === id);
    if (techToDelete?.role === TechnicianRole.Owner) {
        setToast("Cannot delete the owner account.");
        return;
    }
    if (window.confirm('Are you sure you want to delete this technician?')) {
        setTechnicians(prev => prev.filter(t => t.id !== id));
        setToast('Technician deleted successfully.');
    }
  };
  
  const createJobCard = (jobData: {
      id: string;
      customerName: string;
      customerPhone: string;
      customerAddress?: string;
      brand?: string;
      deviceModel: string;
      imei?: string;
      serialNo?: string;
      issueDescription: string;
      estimatedCost: number;
      advancePayment: number;
      priority: JobPriority;
      photoUrl?: string;
      technicianId?: string;
      usedParts?: UsedPart[];
      laborCost?: number;
  }) => {
      let customer = customers.find(c => c.phone === jobData.customerPhone);
      if (!customer) {
          const newCustomer: Customer = {
              id: `CUS-${Date.now()}`,
              name: jobData.customerName,
              phone: jobData.customerPhone,
              address: jobData.customerAddress || '',
          };
          setCustomers(prev => [newCustomer, ...prev]);
          customer = newCustomer;
      }

      const newOrder: Order = {
          id: jobData.id,
          customer,
          status: OrderStatus.Received,
          createdAt: new Date(),
          updatedAt: new Date(),
          brand: jobData.brand,
          deviceModel: jobData.deviceModel,
          imei: jobData.imei,
          serialNo: jobData.serialNo,
          issueDescription: jobData.issueDescription,
          estimatedCost: jobData.estimatedCost,
          advancePayment: jobData.advancePayment,
          priority: jobData.priority,
          photoUrl: jobData.photoUrl,
          technicianId: jobData.technicianId,
          usedParts: jobData.usedParts || [],
          laborCost: jobData.laborCost || 0,
      };
      setOrders(prev => [newOrder, ...prev]);
      setToast('New job card created successfully!');
      return newOrder;
  };

  const syncData = () => {
    setToast('Syncing data...');
    addNotification({ message: 'Data sync started.', type: 'system' });
    setTimeout(() => {
      setLastSync(new Date().toISOString());
      setToast('Data synced successfully!');
      addNotification({ message: 'Data synced successfully!', type: 'system', read: true });
    }, 1500);
  };
  
  const updateShopProfile = (profile: ShopProfile) => {
    setShopProfile(profile);
    setToast('Shop profile updated successfully!');
  };

  const updateBusinessHours = (hours: BusinessHour[]) => {
    setBusinessHours(hours);
    setToast('Business hours updated successfully!');
  };

  const viewOrderDetails = (orderId: string) => {
    setActiveOrderId(orderId);
    setActiveScreen('OrderDetails');
  };
  
  const viewCustomerDetails = (customerId: string) => {
    setActiveCustomerId(customerId);
    setActiveScreen('CustomerProfile');
  };

  const updateOrderStatus = (orderId: string, status: OrderStatus) => {
    const order = orders.find(o => o.id === orderId);
    if (order) {
        // Handle notifications
        if (status === OrderStatus.ReadyForDelivery) {
            addNotification({
                message: `Order for ${order.deviceModel} is ready for delivery.`,
                type: 'order',
                relatedId: order.id
            });
        }
        
        // Deduct stock when repair starts, if not already started/completed/cancelled
        const repairStartedOrFinished = [OrderStatus.RepairInProgress, OrderStatus.QualityCheck, OrderStatus.ReadyForDelivery, OrderStatus.Delivered, OrderStatus.Cancelled].includes(order.status);
        if (status === OrderStatus.RepairInProgress && !repairStartedOrFinished && order.usedParts && order.usedParts.length > 0) {
            let inventoryUpdated = false;
            const newInventory = inventory.map(invItem => {
                const partUsed = order.usedParts?.find(p => p.inventoryItemId === invItem.id);
                if (partUsed) {
                    inventoryUpdated = true;
                    const newQuantity = invItem.quantity - partUsed.quantity;
                    // Check for low stock notification trigger
                    if (newQuantity <= invItem.lowStockThreshold && invItem.quantity > invItem.lowStockThreshold) {
                         addNotification({
                            message: `Stock for ${invItem.name} is low (${newQuantity} left).`,
                            type: 'inventory',
                            relatedId: invItem.id
                        });
                    }
                    return { ...invItem, quantity: newQuantity };
                }
                return invItem;
            });
            if (inventoryUpdated) {
                setInventory(newInventory);
                setToast('Inventory stock has been deducted.');
            }
        }
        updateOrder({ ...order, status });
        setToast(`Order status updated to "${status}"`);
    }
  };

  const logout = () => {
    const confirmLogout = window.confirm("Are you sure you want to logout? This will clear all local data.");
    if (confirmLogout) {
        Object.keys(localStorage).forEach(key => {
            if (key.startsWith('app_')) {
                localStorage.removeItem(key);
            }
        });
        window.location.reload();
    }
  };

  // App Lock Functions
  const handleSetAppLockEnabled = (enabled: boolean) => {
    setAppLockEnabled(enabled);
    if (enabled) {
      setIsLocked(true); // Show lock screen. It will handle setup if PIN is missing.
    } else {
      setIsLocked(false); // For simplicity, just unlock when disabled.
      setToast("App Lock disabled.");
    }
  };

  const unlockApp = (pin: string): boolean => {
    if (appPin && pin === appPin) {
      setIsLocked(false);
      setToast('App Unlocked!');
      return true;
    }
    return false;
  };

  const setPinAndUnlock = (pin: string) => {
    setAppPin(pin);
    setIsLocked(false);
    setToast('PIN has been set successfully!');
  };

  const changePin = () => {
    setAppPin(null); // Clear current PIN
    setIsLocked(true); // Show lock screen to force setup
    setToast('Please create a new PIN.');
  };

  // --- Gemini Chat Functionality ---
    const sendMessageToAi = async (message: string) => {
    if (!process.env.API_KEY) {
        setToast("API_KEY is not configured for AI Chat.");
        return;
    }
    setChatHistory(prev => [...prev, { role: 'user', text: message }]);
    setIsAiResponding(true);

    try {
        const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

        const context = {
            shopProfile: { name: shopProfile.name },
            stats: {
                activeRepairs: orders.filter(o => ![OrderStatus.Delivered, OrderStatus.Cancelled].includes(o.status)).length,
                lowStockItemCount: inventory.filter(i => i.quantity <= i.lowStockThreshold).length,
                totalCustomers: customers.length,
            },
            lowStockItemsList: inventory.filter(i => i.quantity <= i.lowStockThreshold).map(i => ({ name: i.name, quantity: i.quantity })),
        };

        const systemInstruction = `You are a helpful AI assistant for a mobile repair shop called "${shopProfile.name}".
        You have access to a summary of the shop's data in JSON format. Answer the user's questions based on this data.
        Be friendly, concise, and helpful. If you cannot answer a question with the provided data, say so politely.
        Current shop data summary: ${JSON.stringify(context)}`;

        const responseStream = await ai.models.generateContentStream({
            model: 'gemini-2.5-flash',
            contents: message,
            config: {
                systemInstruction: systemInstruction,
            }
        });

        let currentResponse = '';
        setChatHistory(prev => [...prev, { role: 'model', text: '' }]);
        for await (const chunk of responseStream) {
            currentResponse += chunk.text;
            setChatHistory(prev => {
                const newHistory = [...prev];
                newHistory[newHistory.length - 1].text = currentResponse;
                return newHistory;
            });
        }
    } catch (err) {
        console.error("AI Chat Error:", err);
        const errorMessage = "Sorry, I encountered an error. Please check your API key and network connection.";
        setChatHistory(prev => [...prev, { role: 'model', text: errorMessage }]);
        setToast(errorMessage)
    } finally {
        setIsAiResponding(false);
    }
  };


  return {
    activeScreen,
    setActiveScreen,
    activeOrderId,
    setActiveOrderId,
    activeCustomerId,
    setActiveCustomerId,
    searchQuery,
    setSearchQuery,
    filterQuery,
    setFilterQuery,
    orders,
    customers,
    inventory,
    wholesaleInventory,
    technicians,
    shopProfile,
    owner,
    theme: [theme, setTheme] as [string, typeof setTheme],
    notificationsEnabled: [notificationsEnabled, setNotificationsEnabled] as [boolean, typeof setNotificationsEnabled],
    appLockEnabled: [appLockEnabled, handleSetAppLockEnabled] as [boolean, (enabled: boolean) => void],
    isLocked,
    appPin,
    unlockApp,
    setPinAndUnlock,
    changePin,
    lastSync,
    businessHours,
    toast,
    setToast,
    addOrder,
    updateOrder,
    deleteOrder,
    addCustomer,
    updateCustomer,
    deleteCustomer,
    addInventoryItem,
    updateInventoryItem,
    deleteInventoryItem,
    addWholesaleItem,
    updateWholesaleItem,
    deleteWholesaleItem,
    addTechnician,
    updateTechnician,
    deleteTechnician,
    updateShopProfile,
    updateBusinessHours,
    createJobCard,
    syncData,
    logout,
    viewOrderDetails,
    viewCustomerDetails,
    updateOrderStatus,
    // Notifications
    notifications,
    unreadNotificationsCount,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    // AI Chat
    chatHistory,
    isAiResponding,
    sendMessageToAi,
  };
};

export default useAppData;
