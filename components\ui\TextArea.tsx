
import React from 'react';

interface TextAreaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label: string;
  containerClassName?: string;
}

const TextArea: React.FC<TextAreaProps> = ({ label, id, containerClassName = '', ...props }) => {
  const textAreaId = id || `textarea-${props.name}`;
  return (
    <div className={containerClassName}>
      <label htmlFor={textAreaId} className="block text-sm font-medium text-gray-700 mb-1">
        {label}
      </label>
      <textarea
        id={textAreaId}
        className="w-full bg-gray-50 border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-accent/50 focus:border-accent transition-colors"
        {...props}
      />
    </div>
  );
};

export default TextArea;
