

import React, { useState } from 'react';
import { useApp } from '../../context/AppContext';
import type { Order } from '../../types';
import { OrderStatus } from '../../types';
import Card from '../ui/Card';
import Button from '../ui/Button';
import StatusBadge from '../ui/StatusBadge';
import PriorityBadge from '../ui/PriorityBadge';
import { PlusCircle, ChevronRight, Briefcase, Trash2, Smartphone, Edit, Phone, MessageCircle } from 'lucide-react';

const OrderItem: React.FC<{ order: Order }> = ({ order }) => {
  const { technicians, deleteOrder, viewOrderDetails, setActiveScreen, setActiveOrderId } = useApp();
  const assignedTechnician = technicians.find(t => t.id === order.technicianId);

  const handleEditClick = () => {
    if (setActiveOrderId) {
        setActiveOrderId(order.id);
        setActiveScreen('EditOrder');
    }
  };
  
  return (
    <Card className="mb-4 !p-0 overflow-hidden">
        <div className="flex gap-4 p-4">
            {/* Image */}
            <div className="w-24 h-24 flex-shrink-0 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                {order.photoUrl ? (
                    <img src={order.photoUrl} alt={order.deviceModel} className="w-full h-full object-cover" />
                ) : (
                    <Smartphone size={32} className="text-gray-400" />
                )}
            </div>

            {/* Details */}
            <div className="flex-grow flex flex-col min-w-0">
                <div className="flex justify-between items-start">
                    <div className="flex-1 pr-2 min-w-0">
                        <p className="font-bold text-gray-800 truncate" title={order.deviceModel}>{order.deviceModel}</p>
                        <p className="text-sm text-gray-500 truncate" title={`${order.customer.name} • ${order.customer.phone}`}>{order.customer.name} &bull; {order.customer.phone}</p>
                    </div>
                    <div className="flex flex-col items-end gap-2 flex-shrink-0">
                        <StatusBadge status={order.status} />
                        <PriorityBadge priority={order.priority} />
                    </div>
                </div>
                
                <p className="text-sm text-gray-600 my-2 line-clamp-2 flex-grow">{order.issueDescription}</p>

                {assignedTechnician && (
                    <div className="mt-auto flex items-center gap-2 text-sm text-gray-500">
                        <Briefcase size={14} className="text-accent" />
                        <span className="text-xs">Assigned: <span className="font-semibold">{assignedTechnician.name}</span></span>
                    </div>
                )}
            </div>
        </div>
        <div className="bg-gray-50 px-4 py-3 flex justify-between items-center border-t border-gray-100">
            <div>
                <span className="text-xs text-gray-500">Est. Cost</span>
                <p className="font-bold text-accent">₹{order.estimatedCost.toLocaleString('en-IN')}</p>
            </div>
            <div className="flex items-center gap-2">
                <a href={`tel:${order.customer.phone}`} title="Call Customer" className="p-2 rounded-full text-green-600 hover:bg-green-50 transition-colors">
                    <Phone size={18} />
                </a>
                <a href={`https://wa.me/${order.customer.phone}`} title="WhatsApp Customer" target="_blank" rel="noopener noreferrer" className="p-2 rounded-full text-green-600 hover:bg-green-50 transition-colors">
                    <MessageCircle size={18} />
                </a>
                <div className="w-px h-6 bg-gray-200 mx-1"></div>
                 <button onClick={() => viewOrderDetails(order.id)} title="View Details" className="p-2 rounded-full text-gray-600 hover:bg-gray-100 hover:text-accent transition-colors">
                    <ChevronRight size={20} />
                </button>
                <Button variant="ghost" size="sm" title="Edit Order" className="!text-gray-600 hover:!bg-gray-100 !p-2" onClick={handleEditClick}>
                    <Edit size={16} />
                </Button>
                <Button variant="ghost" size="sm" title="Delete Order" className="!text-red-600 hover:!bg-red-50 !p-2" onClick={() => deleteOrder(order.id)}>
                    <Trash2 size={16} />
                </Button>
            </div>
        </div>
    </Card>
  );
};


const OrdersScreen: React.FC = () => {
  const { orders, setActiveScreen, filterQuery } = useApp();
  const [statusFilter, setStatusFilter] = useState('Active');

  const filterButtons = ['Active', 'Ready', 'Completed', 'Cancelled', 'All'];

  const filteredOrders = orders.filter(order => {
    switch (statusFilter) {
      case 'Active':
        return [
          OrderStatus.Received,
          OrderStatus.Diagnosing,
          OrderStatus.RepairInProgress,
          OrderStatus.QualityCheck,
        ].includes(order.status);
      case 'Ready':
        return order.status === OrderStatus.ReadyForDelivery;
      case 'Completed':
        return order.status === OrderStatus.Delivered;
      case 'Cancelled':
        return order.status === OrderStatus.Cancelled;
      case 'All':
      default:
        return true;
    }
  }).filter(order => {
    if (!filterQuery) return true;
    const lowerQuery = filterQuery.toLowerCase();
    return (
        order.id.toLowerCase().includes(lowerQuery) ||
        order.deviceModel.toLowerCase().includes(lowerQuery) ||
        order.customer.name.toLowerCase().includes(lowerQuery) ||
        order.customer.phone.includes(lowerQuery)
    );
  });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-800">Repair Orders</h2>
        <Button size="sm" onClick={() => setActiveScreen('NewOrder')}>
          <PlusCircle size={16} />
          New Order
        </Button>
      </div>
      
      <div className="flex space-x-2 overflow-x-auto pb-2 -mx-4 px-4">
        {filterButtons.map(f => (
          <button 
            key={f}
            onClick={() => setStatusFilter(f)}
            className={`px-4 py-2 text-sm font-semibold rounded-full transition-colors whitespace-nowrap ${statusFilter === f ? 'bg-accent text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
          >
            {f}
          </button>
        ))}
      </div>

      <div>
        {filteredOrders.length > 0 ? (
          filteredOrders.map(order => <OrderItem key={order.id} order={order} />)
        ) : (
          <Card className="text-center text-gray-500 py-8">
            {filterQuery ? `No orders found for "${filterQuery}"` : 'No orders found for this status.'}
          </Card>
        )}
      </div>
    </div>
  );
};

export default OrdersScreen;