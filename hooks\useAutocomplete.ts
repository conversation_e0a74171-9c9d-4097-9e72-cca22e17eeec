
import { useEffect, useRef } from 'react';
import useGoogleMapsLoader from './useGoogleMapsLoader';

const useAutocomplete = (
    inputRef: React.RefObject<HTMLInputElement>, 
    onPlaceSelected: (place: any) => void
) => {
    const isMapsLoaded = useGoogleMapsLoader();
    const autocompleteRef = useRef<any | null>(null);

    useEffect(() => {
        if (!isMapsLoaded || !inputRef.current || autocompleteRef.current) {
            return;
        }

        const google = (window as any).google;
        if (!google) {
            return;
        }

        // Restrict to India as per app context (₹ symbol used)
        const autocomplete = new google.maps.places.Autocomplete(inputRef.current, {
            fields: ["formatted_address"],
            types: ["address"],
            componentRestrictions: { country: "in" } 
        });
        autocompleteRef.current = autocomplete;

        const listener = autocomplete.addListener('place_changed', () => {
            const place = autocomplete.getPlace();
            if (place && place.formatted_address) {
                onPlaceSelected(place);
            }
        });

        return () => {
            // Clean up the listener when the component unmounts or dependencies change.
            if (google && autocompleteRef.current) {
                google.maps.event.clearInstanceListeners(autocompleteRef.current);
            }
        };

    }, [isMapsLoaded, inputRef, onPlaceSelected]);
};

export default useAutocomplete;
