import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../app_state.dart';

class BusinessSettingsScreen extends StatefulWidget {
  const BusinessSettingsScreen({super.key});

  @override
  State<BusinessSettingsScreen> createState() => _BusinessSettingsScreenState();
}

class _BusinessSettingsScreenState extends State<BusinessSettingsScreen> {
  final _taxRateController = TextEditingController();
  final _laborRateController = TextEditingController();
  final _warrantyPeriodController = TextEditingController();

  @override
  void initState() {
    super.initState();
    final appState = context.read<AppState>();
    _taxRateController.text = appState.appSettings.taxRate.toString();
    _laborRateController.text = '500'; // Default labor rate
    _warrantyPeriodController.text = '30'; // Default warranty days
  }

  @override
  void dispose() {
    _taxRateController.dispose();
    _laborRateController.dispose();
    _warrantyPeriodController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.black, Color(0xFF0A0A0A)],
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          title: const Text(
            'Business Settings',
            style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
          ),
        ),
        body: Consumer<AppState>(
          builder: (context, appState, _) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Tax Settings
                  _buildSectionTitle('Tax Configuration'),
                  const SizedBox(height: 16),
                  _buildSettingsCard([
                    _buildTextFieldTile(
                      'Tax Rate (%)',
                      'GST/VAT percentage',
                      Icons.percent,
                      _taxRateController,
                      onChanged: (value) {
                        final rate = double.tryParse(value);
                        if (rate != null) {
                          appState.updateSetting('taxRate', rate);
                        }
                      },
                    ),
                  ]),

                  const SizedBox(height: 24),

                  // Pricing Settings
                  _buildSectionTitle('Pricing Configuration'),
                  const SizedBox(height: 16),
                  _buildSettingsCard([
                    _buildTextFieldTile(
                      'Default Labor Rate (₹/hour)',
                      'Standard hourly labor charge',
                      Icons.work,
                      _laborRateController,
                    ),
                    _buildTextFieldTile(
                      'Default Warranty (days)',
                      'Standard warranty period',
                      Icons.verified_user,
                      _warrantyPeriodController,
                    ),
                  ]),

                  const SizedBox(height: 24),

                  // Business Hours
                  _buildSectionTitle('Business Hours'),
                  const SizedBox(height: 16),
                  _buildBusinessHoursCard(),

                  const SizedBox(height: 24),

                  // Invoice Settings
                  _buildSectionTitle('Invoice Settings'),
                  const SizedBox(height: 16),
                  _buildSettingsCard([
                    _buildListTile(
                      'Invoice Template',
                      'Choose invoice design',
                      Icons.receipt_long,
                      onTap: () => _showInvoiceTemplates(),
                    ),
                    _buildListTile(
                      'Invoice Numbering',
                      'Configure invoice number format',
                      Icons.format_list_numbered,
                      onTap: () => _showInvoiceNumbering(),
                    ),
                  ]),

                  const SizedBox(height: 24),

                  // Payment Methods
                  _buildSectionTitle('Payment Methods'),
                  const SizedBox(height: 16),
                  _buildPaymentMethodsCard(),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        color: Colors.white,
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildSettingsCard(List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF1F1F1F),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white12),
      ),
      child: Column(children: children),
    );
  }

  Widget _buildTextFieldTile(
    String title,
    String subtitle,
    IconData icon,
    TextEditingController controller, {
    Function(String)? onChanged,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: const Color(0xFFE50914).withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: const Color(0xFFE50914), size: 20),
      ),
      title: Text(title, style: const TextStyle(color: Colors.white)),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(subtitle, style: const TextStyle(color: Colors.white70, fontSize: 12)),
          const SizedBox(height: 8),
          SizedBox(
            width: 120,
            child: TextField(
              controller: controller,
              style: const TextStyle(color: Colors.white, fontSize: 14),
              decoration: const InputDecoration(
                isDense: true,
                contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                border: OutlineInputBorder(),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white24),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Color(0xFFE50914)),
                ),
              ),
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListTile(String title, String subtitle, IconData icon, {VoidCallback? onTap}) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: const Color(0xFFE50914).withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: const Color(0xFFE50914), size: 20),
      ),
      title: Text(title, style: const TextStyle(color: Colors.white)),
      subtitle: Text(subtitle, style: const TextStyle(color: Colors.white70, fontSize: 12)),
      trailing: const Icon(Icons.chevron_right, color: Colors.white54),
      onTap: onTap,
    );
  }

  Widget _buildBusinessHoursCard() {
    final days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    final defaultHours = '9:00 AM - 6:00 PM';

    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF1F1F1F),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white12),
      ),
      child: Column(
        children: days.map((day) {
          final isLast = day == days.last;
          return Column(
            children: [
              ListTile(
                title: Text(day, style: const TextStyle(color: Colors.white)),
                trailing: Text(
                  day == 'Sunday' ? 'Closed' : defaultHours,
                  style: TextStyle(
                    color: day == 'Sunday' ? Colors.red : Colors.white70,
                    fontSize: 12,
                  ),
                ),
                onTap: () => _editBusinessHours(day),
              ),
              if (!isLast) const Divider(color: Colors.white12, height: 1),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildPaymentMethodsCard() {
    final paymentMethods = [
      {'name': 'Cash', 'enabled': true, 'icon': Icons.money},
      {'name': 'UPI', 'enabled': true, 'icon': Icons.qr_code},
      {'name': 'Card', 'enabled': true, 'icon': Icons.credit_card},
      {'name': 'Bank Transfer', 'enabled': false, 'icon': Icons.account_balance},
    ];

    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF1F1F1F),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white12),
      ),
      child: Column(
        children: paymentMethods.map((method) {
          final isLast = method == paymentMethods.last;
          return Column(
            children: [
              ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFFE50914).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(method['icon'] as IconData, color: const Color(0xFFE50914), size: 20),
                ),
                title: Text(method['name'] as String, style: const TextStyle(color: Colors.white)),
                trailing: Switch(
                  value: method['enabled'] as bool,
                  onChanged: (value) {
                    // TODO: Update payment method status
                  },
                  activeColor: const Color(0xFFE50914),
                ),
              ),
              if (!isLast) const Divider(color: Colors.white12, height: 1),
            ],
          );
        }).toList(),
      ),
    );
  }

  void _editBusinessHours(String day) {
    // TODO: Show time picker dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1F1F1F),
        title: Text('Edit $day Hours', style: const TextStyle(color: Colors.white)),
        content: const Text(
          'Time picker coming soon!',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK', style: TextStyle(color: Color(0xFFE50914))),
          ),
        ],
      ),
    );
  }

  void _showInvoiceTemplates() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1F1F1F),
        title: const Text('Invoice Templates', style: TextStyle(color: Colors.white)),
        content: const Text(
          'Template selection coming soon!',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK', style: TextStyle(color: Color(0xFFE50914))),
          ),
        ],
      ),
    );
  }

  void _showInvoiceNumbering() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1F1F1F),
        title: const Text('Invoice Numbering', style: TextStyle(color: Colors.white)),
        content: const Text(
          'Numbering format configuration coming soon!',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK', style: TextStyle(color: Color(0xFFE50914))),
          ),
        ],
      ),
    );
  }
}
