import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../app_state.dart';
import '../models.dart';
import 'add_inventory_item.dart';

class InventoryDetailsScreen extends StatefulWidget {
  final InventoryItem item;

  const InventoryDetailsScreen({super.key, required this.item});

  @override
  State<InventoryDetailsScreen> createState() => _InventoryDetailsScreenState();
}

class _InventoryDetailsScreenState extends State<InventoryDetailsScreen> {
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _adjustmentController = TextEditingController();
  final TextEditingController _reasonController = TextEditingController();

  @override
  void dispose() {
    _quantityController.dispose();
    _adjustmentController.dispose();
    _reasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.black, Color(0xFF0A0A0A)],
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          title: const Text(
            'Inventory Details',
            style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.edit, color: Colors.white),
              onPressed: () => Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (_) => AddInventoryItemScreen(existingItem: widget.item),
                ),
              ),
            ),
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () => _showDeleteDialog(),
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Item Image and Basic Info
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: const Color(0xFF1F1F1F),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: widget.item.isOutOfStock 
                        ? Colors.red.withOpacity(0.5)
                        : widget.item.isLowStock 
                            ? Colors.orange.withOpacity(0.5)
                            : Colors.white12,
                  ),
                ),
                child: Column(
                  children: [
                    // Item Image
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: _getCategoryColor(widget.item.category).withOpacity(0.2),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: widget.item.imageUrl != null
                          ? ClipRRect(
                              borderRadius: BorderRadius.circular(16),
                              child: Image.file(
                                File(widget.item.imageUrl!),
                                fit: BoxFit.cover,
                                errorBuilder: (_, __, ___) => Icon(
                                  _getCategoryIcon(widget.item.category),
                                  color: _getCategoryColor(widget.item.category),
                                  size: 60,
                                ),
                              ),
                            )
                          : Icon(
                              _getCategoryIcon(widget.item.category),
                              color: _getCategoryColor(widget.item.category),
                              size: 60,
                            ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Item Name
                    Text(
                      widget.item.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    
                    if (widget.item.brand != null || widget.item.model != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          '${widget.item.brand ?? ''} ${widget.item.model ?? ''}'.trim(),
                          style: const TextStyle(color: Colors.white70, fontSize: 16),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    
                    if (widget.item.description != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 12),
                        child: Text(
                          widget.item.description!,
                          style: const TextStyle(color: Colors.white60, fontSize: 14),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    
                    const SizedBox(height: 16),
                    
                    // Status Badge
                    if (widget.item.isOutOfStock)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Text(
                          'OUT OF STOCK',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      )
                    else if (widget.item.isLowStock)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.orange,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Text(
                          'LOW STOCK',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      )
                    else
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Text(
                          'IN STOCK',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Stock Information
              _buildInfoSection(
                'Stock Information',
                [
                  _InfoRow('Current Quantity', '${widget.item.quantity}'),
                  _InfoRow('Low Stock Alert', '${widget.item.lowStockThreshold}'),
                  _InfoRow('Category', _getCategoryDisplayName(widget.item.category)),
                ],
              ),
              
              const SizedBox(height: 20),
              
              // Pricing Information
              _buildInfoSection(
                'Pricing Information',
                [
                  _InfoRow('Cost Price', '₹${widget.item.costPrice.toStringAsFixed(2)}'),
                  _InfoRow('Selling Price', '₹${widget.item.sellingPrice.toStringAsFixed(2)}'),
                  _InfoRow('Profit Margin', '₹${widget.item.profitMargin.toStringAsFixed(2)}'),
                  _InfoRow('Profit %', '${widget.item.profitPercentage.toStringAsFixed(1)}%'),
                ],
              ),
              
              const SizedBox(height: 20),
              
              // Additional Information
              if (widget.item.supplier != null || 
                  widget.item.location != null || 
                  widget.item.barcode != null)
                _buildInfoSection(
                  'Additional Information',
                  [
                    if (widget.item.supplier != null)
                      _InfoRow('Supplier', widget.item.supplier!),
                    if (widget.item.location != null)
                      _InfoRow('Location', widget.item.location!),
                    if (widget.item.barcode != null)
                      _InfoRow('Barcode/SKU', widget.item.barcode!),
                  ],
                ),
              
              const SizedBox(height: 20),
              
              // Quick Actions
              _buildInfoSection(
                'Quick Actions',
                [],
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: _buildActionButton(
                            'Adjust Stock',
                            Icons.edit,
                            Colors.blue,
                            () => _showStockAdjustmentDialog(),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildActionButton(
                            'Quick Update',
                            Icons.update,
                            Colors.green,
                            () => _showQuickUpdateDialog(),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoSection(String title, List<_InfoRow> rows, {Widget? child}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F1F1F),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (rows.isNotEmpty) ...[
            const SizedBox(height: 16),
            ...rows.map((row) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    row.label,
                    style: const TextStyle(color: Colors.white70, fontSize: 14),
                  ),
                  Text(
                    row.value,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            )),
          ],
          if (child != null) ...[
            const SizedBox(height: 16),
            child,
          ],
        ],
      ),
    );
  }

  Widget _buildActionButton(String label, IconData icon, Color color, VoidCallback onPressed) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: [color, color.withOpacity(0.8)]),
        borderRadius: BorderRadius.circular(12),
      ),
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, color: Colors.white, size: 18),
        label: Text(
          label,
          style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
      ),
    );
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1F1F1F),
        title: const Text('Delete Item', style: TextStyle(color: Colors.white)),
        content: Text(
          'Are you sure you want to delete "${widget.item.name}"? This action cannot be undone.',
          style: const TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel', style: TextStyle(color: Colors.white70)),
          ),
          Container(
            decoration: BoxDecoration(
              gradient: const LinearGradient(colors: [Colors.red, Color(0xFFB71C1C)]),
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                final appState = context.read<AppState>();
                await appState.deleteInventoryItem(widget.item.id);
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Inventory item deleted successfully'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  Navigator.of(context).pop();
                }
              },
              style: TextButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: Colors.white,
              ),
              child: const Text('Delete'),
            ),
          ),
        ],
      ),
    );
  }

  void _showStockAdjustmentDialog() {
    _adjustmentController.clear();
    _reasonController.clear();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1F1F1F),
        title: const Text('Adjust Stock', style: TextStyle(color: Colors.white)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Current Stock: ${widget.item.quantity}',
              style: const TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _adjustmentController,
              keyboardType: TextInputType.number,
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(
                labelText: 'Adjustment (+/-)',
                labelStyle: TextStyle(color: Colors.white70),
                hintText: 'e.g., +10 or -5',
                hintStyle: TextStyle(color: Colors.white54),
                filled: true,
                fillColor: Color(0xFF2A2A2A),
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _reasonController,
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(
                labelText: 'Reason (Optional)',
                labelStyle: TextStyle(color: Colors.white70),
                filled: true,
                fillColor: Color(0xFF2A2A2A),
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel', style: TextStyle(color: Colors.white70)),
          ),
          Container(
            decoration: BoxDecoration(
              gradient: const LinearGradient(colors: [Color(0xFFE50914), Color(0xFFB20710)]),
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextButton(
              onPressed: () async {
                final adjustmentText = _adjustmentController.text.trim();
                if (adjustmentText.isEmpty) return;

                int? adjustment = int.tryParse(adjustmentText);
                if (adjustment == null) return;

                Navigator.of(context).pop();
                final appState = context.read<AppState>();
                await appState.adjustInventoryStock(
                  widget.item.id,
                  adjustment,
                  _reasonController.text.trim(),
                );

                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Stock adjusted successfully'),
                      backgroundColor: Color(0xFFE50914),
                    ),
                  );
                  // Refresh the screen by popping and pushing again
                  Navigator.of(context).pop();
                }
              },
              style: TextButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: Colors.white,
              ),
              child: const Text('Adjust'),
            ),
          ),
        ],
      ),
    );
  }

  void _showQuickUpdateDialog() {
    _quantityController.text = widget.item.quantity.toString();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1F1F1F),
        title: const Text('Quick Update Quantity', style: TextStyle(color: Colors.white)),
        content: TextField(
          controller: _quantityController,
          keyboardType: TextInputType.number,
          style: const TextStyle(color: Colors.white),
          decoration: const InputDecoration(
            labelText: 'New Quantity',
            labelStyle: TextStyle(color: Colors.white70),
            filled: true,
            fillColor: Color(0xFF2A2A2A),
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel', style: TextStyle(color: Colors.white70)),
          ),
          Container(
            decoration: BoxDecoration(
              gradient: const LinearGradient(colors: [Color(0xFFE50914), Color(0xFFB20710)]),
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextButton(
              onPressed: () async {
                final newQuantity = int.tryParse(_quantityController.text);
                if (newQuantity == null || newQuantity < 0) return;

                Navigator.of(context).pop();
                final appState = context.read<AppState>();
                await appState.updateInventoryQuantity(widget.item.id, newQuantity);

                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Quantity updated successfully'),
                      backgroundColor: Color(0xFFE50914),
                    ),
                  );
                  // Refresh the screen
                  Navigator.of(context).pop();
                }
              },
              style: TextButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: Colors.white,
              ),
              child: const Text('Update'),
            ),
          ),
        ],
      ),
    );
  }

  Color _getCategoryColor(InventoryCategory category) {
    switch (category) {
      case InventoryCategory.screen:
        return Colors.blue;
      case InventoryCategory.battery:
        return Colors.green;
      case InventoryCategory.charger:
        return Colors.orange;
      case InventoryCategory.speaker:
        return Colors.purple;
      case InventoryCategory.camera:
        return Colors.teal;
      case InventoryCategory.motherboard:
        return Colors.red;
      case InventoryCategory.backCover:
        return Colors.brown;
      case InventoryCategory.flex:
        return Colors.yellow;
      case InventoryCategory.sensor:
        return Colors.pink;
      case InventoryCategory.other:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(InventoryCategory category) {
    switch (category) {
      case InventoryCategory.screen:
        return Icons.phone_android;
      case InventoryCategory.battery:
        return Icons.battery_full;
      case InventoryCategory.charger:
        return Icons.power;
      case InventoryCategory.speaker:
        return Icons.volume_up;
      case InventoryCategory.camera:
        return Icons.camera_alt;
      case InventoryCategory.motherboard:
        return Icons.memory;
      case InventoryCategory.backCover:
        return Icons.phone_iphone;
      case InventoryCategory.flex:
        return Icons.cable;
      case InventoryCategory.sensor:
        return Icons.sensors;
      case InventoryCategory.other:
        return Icons.category;
    }
  }

  String _getCategoryDisplayName(InventoryCategory category) {
    switch (category) {
      case InventoryCategory.screen:
        return 'Screen';
      case InventoryCategory.battery:
        return 'Battery';
      case InventoryCategory.charger:
        return 'Charger';
      case InventoryCategory.speaker:
        return 'Speaker';
      case InventoryCategory.camera:
        return 'Camera';
      case InventoryCategory.motherboard:
        return 'Motherboard';
      case InventoryCategory.backCover:
        return 'Back Cover';
      case InventoryCategory.flex:
        return 'Flex';
      case InventoryCategory.sensor:
        return 'Sensor';
      case InventoryCategory.other:
        return 'Other';
    }
  }
}

class _InfoRow {
  final String label;
  final String value;

  const _InfoRow(this.label, this.value);
}
