import React from 'react';
import { JobPriority } from '../../types';
import { Zap, Star } from 'lucide-react';

interface PriorityBadgeProps {
  priority: JobPriority;
}

const priorityStyles: { [key in JobPriority]: { text: string; bg: string; icon?: React.ReactNode } } = {
    [JobPriority.Normal]: { text: 'text-gray-700', bg: 'bg-gray-200' },
    [JobPriority.Urgent]: { text: 'text-orange-800', bg: 'bg-orange-100', icon: <Zap size={12} /> },
    [JobPriority.VIP]: { text: 'text-purple-800', bg: 'bg-purple-100', icon: <Star size={12} /> },
};


const PriorityBadge: React.FC<PriorityBadgeProps> = ({ priority }) => {
  const style = priorityStyles[priority] || priorityStyles[JobPriority.Normal];

  return (
    <span className={`px-2.5 py-1 text-xs font-semibold rounded-full flex items-center gap-1.5 ${style.bg} ${style.text}`}>
      {style.icon}
      <span>{priority}</span>
    </span>
  );
};

export default PriorityBadge;
