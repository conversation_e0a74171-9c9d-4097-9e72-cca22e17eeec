import 'dart:io';

import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class CameraCaptureScreen extends StatefulWidget {
  final CameraLensDirection? initialLens;
  const CameraCaptureScreen({super.key, this.initialLens});

  @override
  State<CameraCaptureScreen> createState() => _CameraCaptureScreenState();
}

class _CameraCaptureScreenState extends State<CameraCaptureScreen> {
  CameraController? _controller;
  Future<void>? _initFuture;
  List<CameraDescription> _cameras = [];
  int _currentIndex = 0;
  bool _isSwitching = false;
  FlashMode _flashMode = FlashMode.off;

  @override
  void initState() {
    super.initState();
    _init();
  }

  Future<void> _init() async {
    try {
      _cameras = await availableCameras();
      if (_cameras.isEmpty) return;
      int defaultIndex;
      if (widget.initialLens != null) {
        defaultIndex = _cameras.indexWhere((c) => c.lensDirection == widget.initialLens);
        if (defaultIndex < 0) {
          // fallback: back camera if requested lens not available
          defaultIndex = _cameras.indexWhere((c) => c.lensDirection == CameraLensDirection.back);
        }
      } else {
        defaultIndex = _cameras.indexWhere((c) => c.lensDirection == CameraLensDirection.back);
      }
      _currentIndex = defaultIndex >= 0 ? defaultIndex : 0;
      await _initializeWith(_cameras[_currentIndex]);
      if (mounted) setState(() {});
    } catch (e) {
      // ignore
    }
  }

  Future<void> _initializeWith(CameraDescription camera) async {
    try {
      await _controller?.dispose();
      final controller = CameraController(
        camera,
        ResolutionPreset.medium,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );
      _controller = controller;
      _initFuture = () async {
        await controller.initialize();
        try {
          await controller.setFlashMode(_flashMode);
        } catch (_) {}
      }();
    } catch (e) {
      // ignore
    }
  }

  Future<void> _cycleFlash() async {
    if (_controller == null) return;
    FlashMode next;
    switch (_flashMode) {
      case FlashMode.off:
        next = FlashMode.auto;
        break;
      case FlashMode.auto:
        next = FlashMode.always;
        break;
      case FlashMode.always:
      case FlashMode.torch:
        next = FlashMode.off;
        break;
    }
    try {
      await _controller!.setFlashMode(next);
      if (mounted) setState(() => _flashMode = next);
    } catch (_) {}
  }

  IconData _flashIcon() {
    switch (_flashMode) {
      case FlashMode.off:
        return Icons.flash_off;
      case FlashMode.auto:
        return Icons.flash_auto;
      case FlashMode.always:
      case FlashMode.torch:
        return Icons.flash_on;
    }
  }

  Future<void> _switchCamera() async {
    if (_cameras.length < 2) return;
    _currentIndex = (_currentIndex + 1) % _cameras.length;
    await _initializeWith(_cameras[_currentIndex]);
    if (mounted) setState(() {});
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final unsupportedDesktop = !kIsWeb && (Platform.isWindows || Platform.isLinux || Platform.isMacOS);
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        title: const Text('Capture Photo'),
        actions: [
          IconButton(
            onPressed: () => _cycleFlash(),
            icon: Icon(_flashIcon(), color: Theme.of(context).colorScheme.onPrimary),
            tooltip: 'Flash',
          ),
          IconButton(
            onPressed: (_cameras.length < 2 || _isSwitching)
                ? null
                : () async {
                    setState(() => _isSwitching = true);
                    await _switchCamera();
                    if (mounted) setState(() => _isSwitching = false);
                  },
            icon: Icon(Icons.cameraswitch, color: Theme.of(context).colorScheme.onPrimary),
            tooltip: _cameras.length < 2 ? 'Only one camera available' : 'Switch camera',
          ),
        ],
      ),
      body: unsupportedDesktop || _controller == null
          ? const Center(child: Text('Live camera not supported on this platform.'))
          : FutureBuilder(
              future: _initFuture,
              builder: (context, snap) {
                if (snap.connectionState != ConnectionState.done) {
                  return const Center(child: CircularProgressIndicator());
                }
                return Stack(
                  children: [
                    Center(child: CameraPreview(_controller!)),
                    // Bottom controls overlay (always visible)
                    Positioned(
                      bottom: 24,
                      left: 16,
                      right: 16,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Flash toggle
                          CircleAvatar(
                            backgroundColor: Colors.black54,
                            child: IconButton(
                              icon: Icon(_flashIcon(), color: Colors.white),
                              onPressed: _cycleFlash,
                              tooltip: 'Flash',
                            ),
                          ),
                          // Shutter
                          FloatingActionButton.large(
                            onPressed: () async {
                              try {
                                if (!_controller!.value.isInitialized) return;
                                final file = await _controller!.takePicture();
                                if (!mounted) return;
                                Navigator.of(context).pop(file.path);
                              } catch (_) {}
                            },
                            child: const Icon(Icons.camera_alt),
                          ),
                          // Switch camera
                          CircleAvatar(
                            backgroundColor: Colors.black54,
                            child: IconButton(
                              icon: const Icon(Icons.cameraswitch, color: Colors.white),
                              onPressed: (_cameras.length < 2 || _isSwitching)
                                  ? null
                                  : () async {
                                      setState(() => _isSwitching = true);
                                      await _switchCamera();
                                      if (mounted) setState(() => _isSwitching = false);
                                    },
                              tooltip: _cameras.length < 2 ? 'Only one camera' : 'Switch camera',
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              },
            ),
    );
  }
}

