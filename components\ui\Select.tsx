import React, { ReactNode } from 'react';
import { ChevronDown } from 'lucide-react';

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label: string;
  icon?: ReactNode;
  containerClassName?: string;
  children: ReactNode;
}

const Select: React.FC<SelectProps> = ({ label, icon, id, containerClassName = '', children, ...props }) => {
  const selectId = id || `select-${props.name}`;
  return (
    <div className={containerClassName}>
      <label htmlFor={selectId} className="block text-sm font-medium text-gray-700 mb-1">
        {label}
      </label>
      <div className="relative">
        {icon && (
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <span className="text-gray-500">{icon}</span>
          </div>
        )}
        <select
          id={selectId}
          className={`w-full bg-gray-50 border border-gray-300 rounded-lg py-2.5 pr-10 focus:outline-none focus:ring-2 focus:ring-accent/50 focus:border-accent transition-colors appearance-none
            ${icon ? 'pl-10' : 'pl-4'}
          `}
          {...props}
        >
          {children}
        </select>
        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-gray-700">
            <ChevronDown size={18} />
        </div>
      </div>
    </div>
  );
};

export default Select;
