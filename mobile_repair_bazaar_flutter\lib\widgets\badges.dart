import 'package:flutter/material.dart';
import '../models.dart';

class StatusBadge extends StatelessWidget {
  final OrderStatus status;
  const StatusBadge({super.key, required this.status});

  @override
  Widget build(BuildContext context) {
    final map = <OrderStatus, Color>{
      OrderStatus.received: Colors.grey.shade300,
      OrderStatus.diagnosing: Colors.orange.shade200,
      OrderStatus.repairInProgress: Colors.blue.shade200,
      OrderStatus.qualityCheck: Colors.indigo.shade200,
      OrderStatus.readyForDelivery: Colors.green.shade200,
      OrderStatus.delivered: Colors.purple.shade200,
      OrderStatus.cancelled: Colors.red.shade200,
    };
    final text = <OrderStatus, String>{
      OrderStatus.received: 'Received',
      OrderStatus.diagnosing: 'Diagnosing',
      OrderStatus.repairInProgress: 'Repair in Progress',
      OrderStatus.qualityCheck: 'Quality Check',
      OrderStatus.readyForDelivery: 'Ready for Delivery',
      OrderStatus.delivered: 'Delivered',
      OrderStatus.cancelled: 'Cancelled',
    };
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: map[status],
        borderRadius: BorderRadius.circular(999),
      ),
      child: Text(text[status]!, style: const TextStyle(fontSize: 11, fontWeight: FontWeight.w600)),
    );
  }
}

class PriorityBadge extends StatelessWidget {
  final JobPriority priority;
  const PriorityBadge({super.key, required this.priority});

  @override
  Widget build(BuildContext context) {
    final style = switch (priority) {
      JobPriority.normal => (Colors.grey.shade200, Colors.grey.shade800, null),
      JobPriority.urgent => (Colors.orange.shade100, Colors.orange.shade800, Icons.bolt),
      JobPriority.vip => (Colors.purple.shade100, Colors.purple.shade800, Icons.star),
    };
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(color: style.$1, borderRadius: BorderRadius.circular(999)),
      child: Row(mainAxisSize: MainAxisSize.min, children: [
        if (style.$3 != null) ...[
          Icon(style.$3, size: 12, color: style.$2),
          const SizedBox(width: 4),
        ],
        Text(priority.name.toUpperCase(), style: TextStyle(fontSize: 10, fontWeight: FontWeight.w700, color: style.$2)),
      ]),
    );
  }
}


