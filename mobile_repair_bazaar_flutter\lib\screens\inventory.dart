import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../app_state.dart';
import '../models.dart';
import 'add_inventory_item.dart';
import 'inventory_details.dart';

class InventoryScreen extends StatefulWidget {
  const InventoryScreen({super.key});

  @override
  State<InventoryScreen> createState() => _InventoryScreenState();
}

class _InventoryScreenState extends State<InventoryScreen> {
  String searchQuery = '';
  InventoryCategory? selectedCategory;
  String sortBy = 'name'; // name, quantity, price, category
  bool showLowStockOnly = false;
  final TextEditingController searchController = TextEditingController();

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  List<InventoryItem> _getFilteredItems(List<InventoryItem> items) {
    var filtered = items.where((item) {
      final matchesSearch = searchQuery.isEmpty ||
          item.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
          (item.brand?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false) ||
          (item.model?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false);
      
      final matchesCategory = selectedCategory == null || item.category == selectedCategory;
      final matchesLowStock = !showLowStockOnly || item.isLowStock;
      
      return matchesSearch && matchesCategory && matchesLowStock;
    }).toList();

    // Sort items
    switch (sortBy) {
      case 'name':
        filtered.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'quantity':
        filtered.sort((a, b) => b.quantity.compareTo(a.quantity));
        break;
      case 'price':
        filtered.sort((a, b) => b.sellingPrice.compareTo(a.sellingPrice));
        break;
      case 'category':
        filtered.sort((a, b) => a.category.name.compareTo(b.category.name));
        break;
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.black, Color(0xFF0A0A0A)],
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFE50914),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.inventory_2_rounded, color: Colors.white, size: 20),
              ),
              const SizedBox(width: 12),
              const Text('Inventory', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white)),
            ],
          ),
          actions: [
            Container(
              margin: const EdgeInsets.only(right: 16),
              decoration: BoxDecoration(
                gradient: const LinearGradient(colors: [Color(0xFFE50914), Color(0xFFB20710)]),
                borderRadius: BorderRadius.circular(12),
              ),
              child: IconButton(
                icon: const Icon(Icons.add, color: Colors.white),
                onPressed: () => Navigator.of(context).push(
                  MaterialPageRoute(builder: (_) => const AddInventoryItemScreen()),
                ),
              ),
            ),
          ],
        ),
        body: Consumer<AppState>(
          builder: (context, state, _) {
            final filteredItems = _getFilteredItems(state.inventory);
            
            return Column(
              children: [
                // Search and Filters
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      // Search Bar
                      Container(
                        decoration: BoxDecoration(
                          color: const Color(0xFF1F1F1F),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.white12),
                        ),
                        child: TextField(
                          controller: searchController,
                          style: const TextStyle(color: Colors.white),
                          decoration: const InputDecoration(
                            hintText: 'Search inventory...',
                            hintStyle: TextStyle(color: Colors.white54),
                            prefixIcon: Icon(Icons.search, color: Colors.white70),
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.all(16),
                          ),
                          onChanged: (value) => setState(() => searchQuery = value),
                        ),
                      ),
                      const SizedBox(height: 16),
                      
                      // Filter Row
                      SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: [
                            _FilterChip(
                              label: 'All Categories',
                              selected: selectedCategory == null,
                              onSelected: () => setState(() => selectedCategory = null),
                            ),
                            ...InventoryCategory.values.map((category) => _FilterChip(
                              label: _getCategoryDisplayName(category),
                              selected: selectedCategory == category,
                              onSelected: () => setState(() => selectedCategory = category),
                            )),
                          ],
                        ),
                      ),
                      const SizedBox(height: 12),
                      
                      // Sort and Options Row
                      Row(
                        children: [
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12),
                              decoration: BoxDecoration(
                                color: const Color(0xFF1F1F1F),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.white12),
                              ),
                              child: DropdownButton<String>(
                                value: sortBy,
                                dropdownColor: const Color(0xFF1F1F1F),
                                style: const TextStyle(color: Colors.white),
                                underline: const SizedBox(),
                                isExpanded: true,
                                items: const [
                                  DropdownMenuItem(value: 'name', child: Text('Sort by Name')),
                                  DropdownMenuItem(value: 'quantity', child: Text('Sort by Quantity')),
                                  DropdownMenuItem(value: 'price', child: Text('Sort by Price')),
                                  DropdownMenuItem(value: 'category', child: Text('Sort by Category')),
                                ],
                                onChanged: (value) => setState(() => sortBy = value!),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Container(
                            decoration: BoxDecoration(
                              color: showLowStockOnly ? const Color(0xFFE50914) : const Color(0xFF1F1F1F),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.white12),
                            ),
                            child: IconButton(
                              icon: Icon(
                                Icons.warning_rounded,
                                color: showLowStockOnly ? Colors.white : Colors.white54,
                              ),
                              onPressed: () => setState(() => showLowStockOnly = !showLowStockOnly),
                              tooltip: 'Show Low Stock Only',
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                // Inventory Stats
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFF1F1F1F),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.white12),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _StatItem('Total Items', '${state.inventory.length}', Icons.inventory_2),
                      _StatItem('Low Stock', '${state.lowStockItems.length}', Icons.warning, color: Colors.orange),
                      _StatItem('Out of Stock', '${state.outOfStockItems.length}', Icons.error, color: Colors.red),
                      _StatItem('Value', '₹${state.totalInventoryValue.toStringAsFixed(0)}', Icons.currency_rupee, color: Colors.green),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Inventory List
                Expanded(
                  child: filteredItems.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                padding: const EdgeInsets.all(24),
                                decoration: BoxDecoration(
                                  color: const Color(0xFF1F1F1F),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: const Icon(Icons.inventory_2_outlined, color: Colors.white54, size: 48),
                              ),
                              const SizedBox(height: 16),
                              const Text('No inventory items found', style: TextStyle(color: Colors.white70, fontSize: 18)),
                            ],
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          itemCount: filteredItems.length,
                          itemBuilder: (context, index) => _InventoryItemCard(
                            item: filteredItems[index],
                            onTap: () => Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (_) => InventoryDetailsScreen(item: filteredItems[index]),
                              ),
                            ),
                          ),
                        ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  String _getCategoryDisplayName(InventoryCategory category) {
    switch (category) {
      case InventoryCategory.screen:
        return 'Screen';
      case InventoryCategory.battery:
        return 'Battery';
      case InventoryCategory.charger:
        return 'Charger';
      case InventoryCategory.speaker:
        return 'Speaker';
      case InventoryCategory.camera:
        return 'Camera';
      case InventoryCategory.motherboard:
        return 'Motherboard';
      case InventoryCategory.backCover:
        return 'Back Cover';
      case InventoryCategory.flex:
        return 'Flex';
      case InventoryCategory.sensor:
        return 'Sensor';
      case InventoryCategory.other:
        return 'Other';
    }
  }
}

class _FilterChip extends StatelessWidget {
  final String label;
  final bool selected;
  final VoidCallback onSelected;

  const _FilterChip({
    required this.label,
    required this.selected,
    required this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: selected,
        onSelected: (_) => onSelected(),
        backgroundColor: const Color(0xFF1F1F1F),
        selectedColor: const Color(0xFFE50914),
        labelStyle: TextStyle(
          color: selected ? Colors.white : Colors.white70,
          fontWeight: selected ? FontWeight.bold : FontWeight.normal,
        ),
        side: BorderSide(color: selected ? const Color(0xFFE50914) : Colors.white24),
      ),
    );
  }
}

class _StatItem extends StatelessWidget {
  final String label;
  final String value;
  final IconData icon;
  final Color? color;

  const _StatItem(this.label, this.value, this.icon, {this.color});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Icon(icon, color: color ?? Colors.white70, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            color: color ?? Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        Text(
          label,
          style: const TextStyle(color: Colors.white54, fontSize: 12),
        ),
      ],
    );
  }
}

class _InventoryItemCard extends StatelessWidget {
  final InventoryItem item;
  final VoidCallback onTap;

  const _InventoryItemCard({
    required this.item,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF1F1F1F),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: item.isOutOfStock
              ? Colors.red.withOpacity(0.5)
              : item.isLowStock
                  ? Colors.orange.withOpacity(0.5)
                  : Colors.white12,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Item Image/Icon
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: _getCategoryColor(item.category).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: item.imageUrl != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: Image.file(
                            File(item.imageUrl!),
                            fit: BoxFit.cover,
                            errorBuilder: (_, __, ___) => Icon(
                              _getCategoryIcon(item.category),
                              color: _getCategoryColor(item.category),
                              size: 30,
                            ),
                          ),
                        )
                      : Icon(
                          _getCategoryIcon(item.category),
                          color: _getCategoryColor(item.category),
                          size: 30,
                        ),
                ),
                const SizedBox(width: 16),

                // Item Details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              item.name,
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (item.isOutOfStock)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Text(
                                'OUT OF STOCK',
                                style: TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
                              ),
                            )
                          else if (item.isLowStock)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.orange,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Text(
                                'LOW STOCK',
                                style: TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      if (item.brand != null || item.model != null)
                        Text(
                          '${item.brand ?? ''} ${item.model ?? ''}'.trim(),
                          style: const TextStyle(color: Colors.white70, fontSize: 14),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Text(
                              'Qty: ${item.quantity}',
                              style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '₹${item.sellingPrice.toStringAsFixed(0)}',
                            style: const TextStyle(
                              color: Color(0xFFE50914),
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          const Spacer(),
                          const Icon(Icons.chevron_right, color: Colors.white54),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getCategoryColor(InventoryCategory category) {
    switch (category) {
      case InventoryCategory.screen:
        return Colors.blue;
      case InventoryCategory.battery:
        return Colors.green;
      case InventoryCategory.charger:
        return Colors.orange;
      case InventoryCategory.speaker:
        return Colors.purple;
      case InventoryCategory.camera:
        return Colors.teal;
      case InventoryCategory.motherboard:
        return Colors.red;
      case InventoryCategory.backCover:
        return Colors.brown;
      case InventoryCategory.flex:
        return Colors.yellow;
      case InventoryCategory.sensor:
        return Colors.pink;
      case InventoryCategory.other:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(InventoryCategory category) {
    switch (category) {
      case InventoryCategory.screen:
        return Icons.phone_android;
      case InventoryCategory.battery:
        return Icons.battery_full;
      case InventoryCategory.charger:
        return Icons.power;
      case InventoryCategory.speaker:
        return Icons.volume_up;
      case InventoryCategory.camera:
        return Icons.camera_alt;
      case InventoryCategory.motherboard:
        return Icons.memory;
      case InventoryCategory.backCover:
        return Icons.phone_iphone;
      case InventoryCategory.flex:
        return Icons.cable;
      case InventoryCategory.sensor:
        return Icons.sensors;
      case InventoryCategory.other:
        return Icons.category;
    }
  }
}
