
import React, { useState } from 'react';
import { useApp } from '../../context/AppContext';
import type { BusinessHour, DayOfWeek } from '../../types';
import Card from '../ui/Card';
import Button from '../ui/Button';
import Switch from '../ui/Switch';
import { Save, Clock } from 'lucide-react';

const DayRow: React.FC<{
  hour: BusinessHour;
  onToggle: (day: DayOfWeek, isOpen: boolean) => void;
  onTimeChange: (day: DayOfWeek, type: 'openTime' | 'closeTime', value: string) => void;
}> = ({ hour, onToggle, onTimeChange }) => {
  const isClosed = !hour.isOpen;
  return (
    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 p-4 border-b border-gray-100 last:border-b-0">
      <div className="flex items-center justify-between sm:justify-start gap-4">
        <span className={`w-24 font-bold ${isClosed ? 'text-gray-400' : 'text-gray-800'}`}>{hour.day}</span>
        <Switch
          enabled={hour.isOpen}
          onChange={(checked) => onToggle(hour.day, checked)}
          aria-label={`Toggle ${hour.day}`}
        />
        <span className={`text-sm font-medium ${isClosed ? 'text-gray-400' : 'text-green-600'}`}>{isClosed ? 'Closed' : 'Open'}</span>
      </div>
      <div className="flex items-center gap-2">
        <input
          type="time"
          value={hour.openTime}
          onChange={(e) => onTimeChange(hour.day, 'openTime', e.target.value)}
          disabled={isClosed}
          aria-label={`${hour.day} opening time`}
          className={`w-full bg-gray-50 border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-accent/50 focus:border-accent transition-colors disabled:bg-gray-200 disabled:text-gray-400 disabled:cursor-not-allowed`}
        />
        <span className={isClosed ? 'text-gray-400' : 'text-gray-600'}>to</span>
        <input
          type="time"
          value={hour.closeTime}
          onChange={(e) => onTimeChange(hour.day, 'closeTime', e.target.value)}
          disabled={isClosed}
          aria-label={`${hour.day} closing time`}
          className={`w-full bg-gray-50 border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-accent/50 focus:border-accent transition-colors disabled:bg-gray-200 disabled:text-gray-400 disabled:cursor-not-allowed`}
        />
      </div>
    </div>
  );
};


const BusinessHoursScreen: React.FC = () => {
  const { businessHours: initialHours, updateBusinessHours } = useApp();
  const [hours, setHours] = useState<BusinessHour[]>(initialHours);

  const handleToggle = (day: DayOfWeek, isOpen: boolean) => {
    setHours(currentHours =>
      currentHours.map(h => h.day === day ? { ...h, isOpen } : h)
    );
  };

  const handleTimeChange = (day: DayOfWeek, type: 'openTime' | 'closeTime', value: string) => {
    setHours(currentHours =>
      currentHours.map(h => h.day === day ? { ...h, [type]: value } : h)
    );
  };

  const handleSaveChanges = () => {
    updateBusinessHours(hours);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Clock className="w-8 h-8 text-accent" />
        <h2 className="text-2xl font-bold text-gray-800">Business Hours</h2>
      </div>

      <Card className="!p-0 overflow-hidden">
        {hours.map(hour => (
          <DayRow
            key={hour.day}
            hour={hour}
            onToggle={handleToggle}
            onTimeChange={handleTimeChange}
          />
        ))}
      </Card>
      
      <div className="pb-4">
        <Button size="lg" className="w-full" onClick={handleSaveChanges}>
          <Save size={18} />
          Save Changes
        </Button>
      </div>
    </div>
  );
};

export default BusinessHoursScreen;
