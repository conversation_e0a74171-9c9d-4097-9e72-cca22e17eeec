

import React, { useState, useEffect, useRef } from 'react';
import { useApp } from '../../context/AppContext';
import type { ShopProfile } from '../../types';
import Card from '../ui/Card';
import Button from '../ui/Button';
import Input from '../ui/Input';
import TextArea from '../ui/TextArea';
import ActionSheet from '../ui/ActionSheet';
import CameraModal from '../ui/CameraModal';
import { Store, Phone, MapPin, Edit, Save, X, User, Camera as CameraIcon, ImageIcon, UploadCloud, Trash2 } from 'lucide-react';

const InfoRow: React.FC<{ icon: React.ReactNode, label: string, value: string | React.ReactNode, isMultiline?: boolean }> = ({ icon, label, value, isMultiline = false }) => {
    const content = (
        <>
            <div className="flex items-center gap-3 text-sm text-gray-500">
                {icon}
                <span className="font-medium">{label}</span>
            </div>
            <div className={`mt-1 text-gray-800 text-base pl-9 ${isMultiline ? 'whitespace-pre-wrap' : ''}`}>
                {value || 'Not set'}
            </div>
        </>
    );
    
    return <div className="py-2 px-2">{content}</div>;
};

const resizeImage = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (event) => {
            const img = new Image();
            img.src = event.target?.result as string;
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const MAX_WIDTH = 1024; // Increased for banner
                const MAX_HEIGHT = 1024;
                let { width, height } = img;

                if (width > height) {
                    if (width > MAX_WIDTH) {
                        height *= MAX_WIDTH / width;
                        width = MAX_WIDTH;
                    }
                } else {
                    if (height > MAX_HEIGHT) {
                        width *= MAX_HEIGHT / height;
                        height = MAX_HEIGHT;
                    }
                }
                canvas.width = width;
                canvas.height = height;
                const ctx = canvas.getContext('2d');
                if (!ctx) {
                    return reject(new Error('Could not get canvas context'));
                }
                ctx.drawImage(img, 0, 0, width, height);
                resolve(canvas.toDataURL('image/jpeg', 0.9));
            };
            img.onerror = (error) => reject(error);
        };
        reader.onerror = (error) => reject(error);
    });
};

const ShopProfileScreen: React.FC = () => {
    const { shopProfile, updateShopProfile, setToast } = useApp();
    const [isEditing, setIsEditing] = useState(false);
    const [formData, setFormData] = useState<ShopProfile>(shopProfile);
    const [isActionSheetOpen, setActionSheetOpen] = useState(false);
    const [isCameraOpen, setCameraOpen] = useState(false);
    const [imageTarget, setImageTarget] = useState<'avatar' | 'banner' | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        if (!isEditing) {
            setFormData(shopProfile);
        }
    }, [shopProfile, isEditing]);

    const handleSave = () => {
        updateShopProfile(formData);
        setIsEditing(false);
    };

    const handleCancel = () => {
        setFormData(shopProfile);
        setIsEditing(false);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };

    const handleImageUpload = (target: 'avatar' | 'banner') => {
        setImageTarget(target);
        setActionSheetOpen(true);
    };

    const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file && imageTarget) {
            try {
                const resizedImage = await resizeImage(file);
                setFormData(prev => ({ ...prev, [`${imageTarget}Url`]: resizedImage }));
            } catch (error) {
                console.error("Error resizing image:", error);
                setToast("Could not process image.");
            }
        }
        event.target.value = '';
    };
    
    const handleTakePhotoClick = () => {
        setActionSheetOpen(false);
        setCameraOpen(true);
    };
    
    const handlePickFromGalleryClick = () => {
        setActionSheetOpen(false);
        fileInputRef.current?.click();
    };

    const handleCapture = (base64Image: string) => {
        if (imageTarget) {
            setFormData(prev => ({ ...prev, [`${imageTarget}Url`]: base64Image }));
        }
        setCameraOpen(false);
    };

    const avatarSrc = isEditing ? formData.avatarUrl : shopProfile.avatarUrl;
    const bannerSrc = isEditing ? formData.bannerUrl : shopProfile.bannerUrl;

    return (
        <div className="space-y-6 -m-4">
            <input type="file" accept="image/*" ref={fileInputRef} onChange={handleFileChange} className="hidden" />
            <ActionSheet isOpen={isActionSheetOpen} onClose={() => setActionSheetOpen(false)} title={`Change ${imageTarget === 'avatar' ? 'Profile Photo' : 'Banner Image'}`}>
                <Button variant="ghost" className="w-full !justify-start" onClick={handleTakePhotoClick}><CameraIcon size={20}/>Take Photo</Button>
                <Button variant="ghost" className="w-full !justify-start" onClick={handlePickFromGalleryClick}><ImageIcon size={20}/>Choose from Gallery</Button>
            </ActionSheet>
            <CameraModal isOpen={isCameraOpen} onClose={() => setCameraOpen(false)} onCapture={handleCapture} />
            
            <div className="relative">
                <div className="h-40 bg-gray-200 flex items-center justify-center">
                    {bannerSrc ? (
                        <img src={bannerSrc} alt="Shop Banner" className="w-full h-full object-cover" />
                    ) : (
                        <ImageIcon className="w-16 h-16 text-gray-400" />
                    )}
                    {isEditing && (
                         <div className="absolute top-3 right-3 flex gap-2">
                            {formData.bannerUrl && (
                                <button
                                    onClick={() => setFormData(prev => ({ ...prev, bannerUrl: '' }))}
                                    className="bg-red-500/80 text-white p-2 rounded-full shadow-md hover:bg-red-600 transition-all" aria-label="Remove banner image">
                                    <Trash2 size={20} />
                                </button>
                            )}
                            <button 
                                onClick={() => handleImageUpload('banner')}
                                className="bg-black/50 text-white p-2 rounded-full shadow-md hover:bg-black/70 transition-all" aria-label="Change banner image">
                                <CameraIcon size={20} />
                            </button>
                        </div>
                    )}
                </div>

                <div className="absolute top-28 left-1/2 -translate-x-1/2 w-full px-4">
                    <div className="flex flex-col items-center">
                         <div className="relative">
                            {avatarSrc ? (
                                <img src={avatarSrc} alt="Shop Profile" className="w-28 h-28 rounded-full object-cover ring-4 ring-white shadow-lg" />
                            ) : (
                                <div className="w-28 h-28 rounded-full bg-gray-200 flex items-center justify-center ring-4 ring-white shadow-lg">
                                    <User className="w-14 h-14 text-gray-400" />
                                </div>
                            )}
                            {isEditing && (
                                <>
                                    <button onClick={() => handleImageUpload('avatar')} className="absolute -bottom-1 -right-1 bg-accent text-white p-2 rounded-full shadow-md hover:bg-opacity-90 transition-all" aria-label="Change profile photo">
                                        <UploadCloud size={20} />
                                    </button>
                                    {formData.avatarUrl && (
                                        <button
                                            onClick={() => setFormData(prev => ({ ...prev, avatarUrl: '' }))}
                                            className="absolute -bottom-1 -left-1 bg-red-500 text-white p-2 rounded-full shadow-md hover:bg-red-600 transition-all" aria-label="Remove profile photo">
                                            <Trash2 size={20} />
                                        </button>
                                    )}
                                </>
                            )}
                        </div>
                    </div>
                </div>
            </div>


            <div className="p-4 pt-20">
                <Card className="!p-6">
                    <div className="flex justify-between items-center mb-4">
                        <h3 className="text-lg font-bold text-gray-700">Shop Information</h3>
                        {!isEditing && (
                            <Button size="sm" variant="secondary" onClick={() => setIsEditing(true)}>
                                <Edit size={16} />
                                Edit
                            </Button>
                        )}
                    </div>

                    {isEditing ? (
                        <form onSubmit={(e) => { e.preventDefault(); handleSave(); }} className="space-y-4">
                            <Input 
                                label="Shop Name"
                                name="name" 
                                value={formData.name} 
                                onChange={handleChange}
                                icon={<Store size={18} />}
                                required
                            />
                            <Input 
                                label="Contact Phone"
                                name="phone"
                                type="tel"
                                value={formData.phone} 
                                onChange={handleChange}
                                icon={<Phone size={18} />}
                            />
                            <TextArea
                                label="Address"
                                name="address"
                                rows={3}
                                value={formData.address}
                                onChange={handleChange}
                                placeholder="Shop's full address"
                            />
                            <div className="flex gap-2 justify-end pt-4">
                                <Button type="button" variant="ghost" onClick={handleCancel}>
                                    <X size={18} />
                                    Cancel
                                </Button>
                                <Button type="submit">
                                    <Save size={18} />
                                    Save Changes
                                </Button>
                            </div>
                        </form>
                    ) : (
                        <div className="space-y-2">
                            <div className="text-center mb-4">
                                <h2 className="text-2xl font-bold text-gray-800">{shopProfile.name}</h2>
                            </div>
                            <InfoRow icon={<Phone size={20} className="text-accent"/>} label="Contact Phone" value={shopProfile.phone} />
                            <InfoRow 
                                icon={<MapPin size={20} className="text-accent"/>} 
                                label="Address" 
                                value={shopProfile.address} 
                                isMultiline={true}
                            />
                        </div>
                    )}
                </Card>
            </div>
        </div>
    );
};

export default ShopProfileScreen;